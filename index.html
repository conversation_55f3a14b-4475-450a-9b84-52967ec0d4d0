<!doctype html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <meta
            name="description"
            content="Real-time cryptocurrency dashboard with order books and charts"
        />
        <!-- Security hardening: Content Security Policy -->
        <meta http-equiv="Content-Security-Policy"
              content="default-src 'self';
                       connect-src 'self' wss://ws.bitstamp.net wss://stream.bybit.com https://api.bitstamp.net https://api.bybit.com https://www.bitstamp.net https://corsproxy.io https://api.allorigins.win;
                       script-src 'self' 'unsafe-inline' https://s3.tradingview.com https://unpkg.com https://cdn.jsdelivr.net;
                       style-src 'self' 'unsafe-inline';
                       img-src 'self' data: https:;
                       font-src 'self' data:;
                       frame-src 'self' https://s.tradingview.com;">
        <title>Crypto Dashboard</title>
        <link rel="stylesheet" href="styles.css" />
        <link rel="</title>icon" href="favicon.ico" type="image/x-icon" />


    </head>
    <body>
        <!-- Main dashboard container -->
        <div class="main-container">
            <!-- Order books for BTC, ETH, LTC, SOL -->
            <div class="order-books-container">
                <div id="btc-container" class="crypto-container">
                    <div id="btc-loading-overlay" class="loading-overlay">
                        Loading...
                    </div>
                    <canvas
                        id="btc-orderbook-canvas"
                        class="orderbook-canvas"
                    ></canvas>
                    <div id="btc-bias-text" class="bias-text">
                        <span class="metric-title" id="btc-ticker-name"></span>
                        <span
                            class="metric-value"
                            id="btc-balance-percent"
                        ></span>
                    </div>
                    <div id="btc-mid-price-text" class="mid-price-text">
                        <span id="btc-mid-price"></span>
                    </div>
                    <div id="btc-price-scale" class="price-scale">
                        <div class="price-block">
                            <span id="btc-min-price"></span>
                            <span id="btc-lowest-price"></span>
                        </div>
                        <div class="price-block">
                            <span id="btc-max-price"></span>
                            <span id="btc-highest-price"></span>
                        </div>
                    </div>


                </div>
                <div id="eth-container" class="crypto-container">
                    <div id="eth-loading-overlay" class="loading-overlay">
                        Loading...
                    </div>
                    <canvas
                        id="eth-orderbook-canvas"
                        class="orderbook-canvas"
                    ></canvas>
                    <div id="eth-bias-text" class="bias-text">
                        <span class="metric-title" id="eth-ticker-name"></span>
                        <span
                            class="metric-value"
                            id="eth-balance-percent"
                        ></span>
                    </div>
                    <div id="eth-mid-price-text" class="mid-price-text">
                        <span id="eth-mid-price"></span>
                    </div>
                    <div id="eth-price-scale" class="price-scale">
                        <div class="price-block">
                            <span id="eth-min-price"></span>
                            <span id="eth-lowest-price"></span>
                        </div>
                        <div class="price-block">
                            <span id="eth-max-price"></span>
                            <span id="eth-highest-price"></span>
                        </div>
                    </div>




                </div>
                <div id="ltc-container" class="crypto-container">
                    <div id="ltc-loading-overlay" class="loading-overlay">
                        Loading...
                    </div>
                    <canvas
                        id="ltc-orderbook-canvas"
                        class="orderbook-canvas"
                    ></canvas>
                    <div id="ltc-bias-text" class="bias-text">
                        <span class="metric-title" id="ltc-ticker-name"></span>
                        <span
                            class="metric-value"
                            id="ltc-balance-percent"
                        ></span>
                    </div>
                    <div id="ltc-mid-price-text" class="mid-price-text">
                        <span id="ltc-mid-price"></span>
                    </div>
                    <div id="ltc-price-scale" class="price-scale">
                        <div class="price-block">
                            <span id="ltc-min-price"></span>
                            <span id="ltc-lowest-price"></span>
                        </div>
                        <div class="price-block">
                            <span id="ltc-max-price"></span>
                            <span id="ltc-highest-price"></span>
                        </div>
                    </div>




                </div>
                <div id="sol-container" class="crypto-container">
                    <div id="sol-loading-overlay" class="loading-overlay">
                        Loading...
                    </div>
                    <canvas
                        id="sol-orderbook-canvas"
                        class="orderbook-canvas"
                    ></canvas>
                    <div id="sol-bias-text" class="bias-text">
                        <span class="metric-title" id="sol-ticker-name"></span>
                        <span
                            class="metric-value"
                            id="sol-balance-percent"
                        ></span>
                    </div>
                    <div id="sol-mid-price-text" class="mid-price-text">
                        <span id="sol-mid-price"></span>
                    </div>
                    <div id="sol-price-scale" class="price-scale">
                        <div class="price-block">
                            <span id="sol-min-price"></span>
                            <span id="sol-lowest-price"></span>
                        </div>
                        <div class="price-block">
                            <span id="sol-max-price"></span>
                            <span id="sol-highest-price"></span>
                        </div>
                    </div>




                </div>
            </div>
            <!-- Console for liquidations and trades -->
            <div class="console-container">
                <div id="console-capture" class="console-capture">
                    <div
                        id="console-capture-title"
                        class="console-capture-title"
                    >
                        LIQS/TRADES
                    </div>
                    <canvas
                        id="console-title-line"
                        width="85"
                        height="1"
                    ></canvas>
                </div>
            </div>
            <!-- Charts and pair selector -->
            <div class="charts-container">
                <div class="chart-container" data-pair="BTC">
                    <div class="pair-selector">
                        <button
                            type="button"
                            class="pair-button active"
                            data-pair="BTC"
                        >
                            BTC
                        </button>
                        <button
                            type="button"
                            class="pair-button"
                            data-pair="ETH"
                        >
                            ETH
                        </button>
                        <button
                            type="button"
                            class="pair-button"
                            data-pair="LTC"
                        >
                            LTC
                        </button>
                        <button
                            type="button"
                            class="pair-button"
                            data-pair="SOL"
                        >
                            SOL
                        </button>
                        <div class="dropdown">
                            <button
                                type="button"
                                class="pair-button dropdown-toggle"
                                id="more-pairs-btn"
                            >
                                ALTS
                            </button>
                            <div
                                class="dropdown-content"
                                id="more-pairs-dropdown"
                            ></div>
                        </div>
                        <button
                            type="button"
                            class="pair-button"
                            id="tradingview-toggle-btn"
                        >
                            CHART
                        </button>
                        <button
                            type="button"
                            class="pair-button"
                            id="rr-calc-toggle-btn"
                        >
                            RR CALC
                        </button>
                        <button
                            type="button"
                            class="pair-button fullscreen-toggle"
                            id="fullscreen-toggle"
                            title="Toggle Fullscreen Mode"
                        ></button>
                    </div>
                    <div class="price-chart-container">
                        <div class="loading-overlay" id="pair-loading-overlay">
                            Loading BTC data...
                        </div>
                        <div class="price-chart"></div>
                        <div
                            id="popup-chart-wrapper"
                            class="popup-chart-wrapper popup-chart-wrapper-hidden"
                        >
                            <div class="popup-chart-header">
                                <div class="popup-chart-timeframes">
                                    <button
                                        type="button"
                                        class="popup-timeframe-btn"
                                        data-interval="1"
                                    >
                                        1m
                                    </button>
                                    <button
                                        type="button"
                                        class="popup-timeframe-btn"
                                        data-interval="5"
                                    >
                                        5m
                                    </button>
                                    <button
                                        type="button"
                                        class="popup-timeframe-btn"
                                        data-interval="15"
                                    >
                                        15m
                                    </button>
                                    <button
                                        type="button"
                                        class="popup-timeframe-btn"
                                        data-interval="30"
                                    >
                                        30m
                                    </button>
                                    <button
                                        type="button"
                                        class="popup-timeframe-btn active"
                                        data-interval="60"
                                    >
                                        1h
                                    </button>
                                    <button
                                        type="button"
                                        class="popup-timeframe-btn"
                                        data-interval="240"
                                    >
                                        4h
                                    </button>
                                    <button
                                        type="button"
                                        class="popup-timeframe-btn"
                                        data-interval="1D"
                                    >
                                        1D
                                    </button>
                                    <button
                                        type="button"
                                        class="popup-timeframe-btn"
                                        data-interval="1W"
                                    >
                                        1W
                                    </button>
                                    <button
                                        type="button"
                                        class="popup-timeframe-btn"
                                        data-interval="1M"
                                    >
                                        1M
                                    </button>
                                </div>
                                <div class="popup-chart-controls">
                                    <button
                                        type="button"
                                        class="popup-chart-toggle"
                                        title="Toggle Chart Visibility"
                                    >
                                        −
                                    </button>
                                </div>
                            </div>
                            <div
                                id="popup-chart-container"
                                class="popup-chart-container"
                            ></div>
                            <div
                                class="popup-chart-resize-handle"
                                title="Resize"
                            ></div>
                        </div>
                        <!-- RR Calculator Popup -->
                        <div
                            id="rr-calc-popup-wrapper"
                            class="popup-chart-wrapper popup-chart-wrapper-hidden rr-calc-popup"
                        >
                            <div class="popup-chart-header rr-calc-header">
                                <span>Risk-Reward Calculator</span>
                                <div class="popup-chart-controls">
                                    <button type="button" class="popup-chart-toggle" id="rr-calc-close-btn" title="Close">×</button>
                                </div>
                            </div>
                            <div id="rr-calc-popup-container" class="popup-chart-container rr-calc-container"></div>
                            <div class="popup-chart-resize-handle" id="rr-calc-popup-resize" title="Resize"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>


        
        <!-- Connection Manager - loads early to coordinate all WebSocket connections -->
        <script src="utils/connectionManager.js"></script>

        <!-- Performance and Modern Utilities (load early for other modules) -->
        <script src="utils/types.js"></script>
        <script src="utils/memoization.js"></script>
        <script src="utils/eventManager.js"></script>
        <script src="utils/modernAsync.js"></script>
        <script src="utils/modernDataStructures.js"></script>
        <script src="utils/performanceOptimizer.js"></script>
        <script src="utils/moduleSystem.js"></script>
        <script src="utils/websocketUtils.js"></script>
        <script src="utils/modernInit.js"></script>

        <!-- Core dependencies and modular scripts (all deferred for order and performance) -->
        <script src="utils/simpleLoader.js" defer></script>
        <script src="indicators/deltaOIProfile.js" defer></script>

        <script src="utils/consoleFilter.js" defer></script>
        <script src="modules/charts/popupChart/PopupChartUI.js" defer></script>



        <script>
            function initializeDropdown() {
                try {
                    const PAIRS = [
                        "ADA",
                        "AAVE",
                        "AVAX",
                        "DOGE",
                        "DOT",
                        "FIL",
                        "LINK",
                        "MATIC",
                        "UNI",
                        "XRP",
                        "XLM",
                        "MKR",
                        "SUSHI",
                        "COMP",
                        "CRV",
                        "1INCH",
                        "LRC",
                        "FET",
                        "DYDX",
                        "INJ",
                        "AXS",
                        "GRT",
                        "SNX",
                        "YFI",
                        "BAND",
                        "KNC",
                        "ENS",
                        "CVX",
                        "RNDR",
                        "AUDIO",
                        "NEXO",
                        "PEPE",
                        "PERP",
                        "PYTH",
                        "RAD",
                        "GODS",
                        "CTSI",
                        "SKL",
                        "FLR",
                    ];
                    
                    const morePairsBtn = document.getElementById("more-pairs-btn");
                    const morePairsDropdown = document.getElementById("more-pairs-dropdown");
                    const mainPairs = ["BTC", "ETH", "LTC", "SOL"];
                    
                    if (!morePairsBtn || !morePairsDropdown) {
                        console.error("Dropdown elements not found:", {
                            morePairsBtn: !!morePairsBtn,
                            morePairsDropdown: !!morePairsDropdown
                        });
                        return;
                    }
                    
                    morePairsDropdown.innerHTML = "";
                    PAIRS.forEach((pair) => {
                        if (!mainPairs.includes(pair)) {
                            const btn = document.createElement("button");
                            btn.type = "button";
                            btn.className = "dropdown-item";
                            btn.dataset.pair = pair;
                            btn.textContent = pair;
                            btn.addEventListener("click", function () {
                                morePairsDropdown.classList.remove("show");
                                // Switch pair logic: update active state and trigger chart logic
                                document
                                    .querySelectorAll(
                                        ".pair-button[data-pair], .dropdown-item[data-pair]",
                                    )
                                    .forEach((b) => {
                                        b.classList.toggle(
                                            "active",
                                            b.dataset.pair === pair,
                                        );
                                    });
                                // Update loading overlay message to reflect selected pair
                                var loadingOverlay = document.getElementById(
                                    "pair-loading-overlay",
                                );
                                if (loadingOverlay) {
                                    loadingOverlay.textContent =
                                        "Loading " + pair + " data...";
                                }
                                // Reference logic: hide popup chart, switch pair, then show popup chart if it was open
                                const popupChartWrapper =
                                    document.getElementById(
                                        "popup-chart-wrapper",
                                    );
                                const wasPopupOpen =
                                    popupChartWrapper &&
                                    popupChartWrapper.style.display === "flex";
                                if (wasPopupOpen) {
                                    popupChartWrapper.style.display = "none";
                                }
                                if (window.switchPairInternal) {
                                    window.switchPairInternal(pair);
                                }
                                if (wasPopupOpen) {
                                    setTimeout(() => {
                                        popupChartWrapper.style.display =
                                            "flex";
                                        // Show loading indicator
                                        const loadingIndicator =
                                            document.createElement("div");
                                        loadingIndicator.id =
                                            "popup-chart-priority-loading";
                                        loadingIndicator.className =
                                            "loading-indicator";
                                        loadingIndicator.textContent = `Loading ${pair} chart...`;
                                        popupChartWrapper.appendChild(
                                            loadingIndicator,
                                        );
                                        if (window.updatePopupChart) {
                                            window.updatePopupChart(pair);
                                        }
                                        setTimeout(() => {
                                            const indicator =
                                                document.getElementById(
                                                    "popup-chart-priority-loading",
                                                );
                                            if (indicator) indicator.remove();
                                        }, 100);
                                    }, 300);
                                }
                            });
                            morePairsDropdown.appendChild(btn);
                        }
                    });
                    
                    morePairsBtn.addEventListener("click", function (e) {
                        e.stopPropagation();
                        morePairsDropdown.classList.toggle("show");
                    });
                    document.addEventListener("click", function (e) {
                        if (
                            !morePairsBtn.contains(e.target) &&
                            !morePairsDropdown.contains(e.target)
                        ) {
                            morePairsDropdown.classList.remove("show");
                        }
                    });
                    
                    console.log("Dropdown initialized successfully");
                } catch (error) {
                    console.error("Error initializing dropdown:", error);
                }
            }
            
            // Try to initialize on DOMContentLoaded
            document.addEventListener("DOMContentLoaded", function () {
                initializeDropdown();
            });
            
            // Fallback: try immediately if DOM is already ready
            if (document.readyState === 'loading') {
                // DOM is still loading, wait for DOMContentLoaded
            } else {
                // DOM is already ready, initialize immediately
                initializeDropdown();
            }
        </script>
        <style>
        /* RR Calculator Popup Styles */
        #rr-calc-popup-container .container { 
            display: flex; 
            flex-direction: column; 
            gap: 12px; 
            padding: 12px;
        }
        #rr-calc-popup-container .input-row { 
            display: flex; 
            align-items: center; 
            gap: 12px; 
        }
        #rr-calc-popup-container label { 
            font-weight: bold; 
            min-width: 120px; 
            color: #D3D3D3;
            font-size: 14px;
            text-align: right;
        }
        #rr-calc-popup-container input, 
        #rr-calc-popup-container select { 
            padding: 6px; 
            font-size: 14px; 
            background-color: #f0f0f0; 
            border: 1px solid #ccc;
            border-radius: 3px;
            color: #333;
        }
        #rr-calc-popup-container button { 
            padding: 8px; 
            background-color: #007bff; 
            color: white; 
            border: none; 
            cursor: pointer; 
            border-radius: 3px;
            font-size: 14px;
        }
        #rr-calc-popup-container button:hover { 
            background-color: #0056b3; 
        }
        #rr-calc-popup-container #result { 
            margin-top: 15px; 
            font-size: 14px; 
            color: #D3D3D3;
        }
        #rr-calc-popup-container .input-wide { width: 100px; }
        #rr-calc-popup-container .input-narrow { width: 60px; }
        #rr-calc-popup-container .select-narrow { width: 80px; }
        .rr-calc-popup { z-index: 20; display: none; }
        .rr-calc-header { cursor: move; }
        .rr-calc-container { background: rgba(15,20,26,1.0); }
        </style>
        <script>
        // --- RR Calculator Popup Logic ---
        document.addEventListener('DOMContentLoaded', function() {
            // RR Calculator HTML (from rr_calculator.html, body only)
            const rrCalcHtml = `
                <div class=\"container\">\n                    <div class=\"input-row\">\n                        <label for=\"accountSize\">Account ($):</label>\n                        <input type=\"number\" id=\"accountSize\" min=\"0\" max=\"********\" step=\"0.01\" class=\"select-narrow\" required>\n                    </div>\n                    <div class=\"input-row\">\n                        <label for=\"riskPercent\">Risk (%):</label>\n                        <input type=\"number\" id=\"riskPercent\" min=\"0\" max=\"9\" step=\"0.1\" class=\"select-narrow\" required>\n                    </div>\n                    <div class=\"input-row\">\n                        <label for=\"entryPrice\">Entry Price ($):</label>\n                        <input type=\"number\" id=\"entryPrice\" min=\"0\" max=\"********\" step=\"0.01\" class=\"select-narrow\" required>\n                    </div>\n                    <div class=\"input-row\">\n                        <label for=\"stopLossPercent\">SL (%):</label>\n                        <input type=\"number\" id=\"stopLossPercent\" min=\"0\" max=\"99\" step=\"0.1\" class=\"select-narrow\" required>\n                    </div>\n                    <div class=\"input-row\">\n                        <label for=\"rewardRatio\">RR:</label>\n                        <input type=\"number\" id=\"rewardRatio\" min=\"0\" max=\"9\" step=\"0.1\" class=\"select-narrow\" required>\n                    </div>\n                    <div class=\"input-row\">\n                        <label for=\"tradeType\">Trade Type:</label>\n                        <select id=\"tradeType\" required class=\"select-narrow\">\n                            <option value=\"long\">Long</option>\n                            <option value=\"short\">Short</option>\n                        </select>\n                    </div>\n                    <button id=\"rr-calc-calc-btn\">Calculate</button>\n                    <div id=\"result\"></div>\n                </div>\n            `;
            const rrCalcContainer = document.getElementById('rr-calc-popup-container');
            if (rrCalcContainer) {
                rrCalcContainer.innerHTML = rrCalcHtml;
            }
            // Calculator logic
            function calculate() {
                const accountSize = parseFloat(document.getElementById('accountSize').value);
                const riskPercent = parseFloat(document.getElementById('riskPercent').value) / 100;
                const entryPrice = parseFloat(document.getElementById('entryPrice').value);
                const stopLossPercent = parseFloat(document.getElementById('stopLossPercent').value) / 100;
                const rewardRatio = parseFloat(document.getElementById('rewardRatio').value);
                const tradeType = document.getElementById('tradeType').value;
                const riskAmount = accountSize * riskPercent;
                const stopLossDistance = entryPrice * stopLossPercent;
                const positionSize = riskAmount / stopLossDistance;
                const positionSizeDollar = positionSize * entryPrice;
                const openCloseFeeRate = 0.0007; // 0.07% total (open + close)
                const openCloseFees = positionSizeDollar * openCloseFeeRate;

                let stopLossPrice, targetPrice, profit;
                if (tradeType === 'long') {
                    stopLossPrice = entryPrice - stopLossDistance;
                    targetPrice = entryPrice + (rewardRatio * stopLossDistance);
                    profit = (targetPrice - entryPrice) * positionSize;
                } else {
                    stopLossPrice = entryPrice + stopLossDistance;
                    targetPrice = entryPrice - (rewardRatio * stopLossDistance);
                    profit = (entryPrice - targetPrice) * positionSize;
                }

                const result = `
                    Trade Type: ${tradeType.charAt(0).toUpperCase() + tradeType.slice(1)}<br>
                    Risk Amount: $${riskAmount.toFixed(2)}<br>
                    Position Size: ${positionSize.toFixed(4)} units<br>
                    Position Size: $${positionSizeDollar.toFixed(2)}<br>
                    Stop Loss Price: $${stopLossPrice.toFixed(2)}<br>
                    Target Price: $${targetPrice.toFixed(2)}<br>
                    Potential Profit: $${profit.toFixed(2)} (${rewardRatio}R)<br>
                    Total Fees (0.07%): $${openCloseFees.toFixed(2)}
                `;
                document.getElementById('result').innerHTML = result;
            }
            document.getElementById('rr-calc-calc-btn').onclick = calculate;
            // Popup open/close logic
            const rrCalcBtn = document.getElementById('rr-calc-toggle-btn');
            const rrCalcPopup = document.getElementById('rr-calc-popup-wrapper');
            const rrCalcClose = document.getElementById('rr-calc-close-btn');
            rrCalcBtn && rrCalcBtn.addEventListener('click', function() {
                // Hide popup chart if open
                const popupChart = document.getElementById('popup-chart-wrapper');
                if (popupChart && popupChart.style.display === 'flex') {
                    popupChart.style.display = 'none';
                    document.getElementById('tradingview-toggle-btn')?.classList.remove('active');
                }
                rrCalcPopup.style.display = rrCalcPopup.style.display === 'flex' ? 'none' : 'flex';
                if (rrCalcPopup.style.display === 'flex') {
                    rrCalcBtn.classList.add('active');
                } else {
                    rrCalcBtn.classList.remove('active');
                }
            });
            rrCalcClose && rrCalcClose.addEventListener('click', function() {
                rrCalcPopup.style.display = 'none';
                rrCalcBtn.classList.remove('active');
            });
            // Drag logic (reuse popup chart logic)
            (function() {
                const wrapper = rrCalcPopup;
                const header = wrapper.querySelector('.popup-chart-header');
                let isDragging = false, dragOffsetX = 0, dragOffsetY = 0, lastEvent = null;
                header.addEventListener('mousedown', function(e) {
                    isDragging = true;
                    dragOffsetX = e.clientX - wrapper.getBoundingClientRect().left;
                    dragOffsetY = e.clientY - wrapper.getBoundingClientRect().top;
                    document.addEventListener('mousemove', onMouseMove);
                    document.addEventListener('mouseup', onMouseUp);
                });
                function onMouseMove(e) {
                    if (!isDragging) return;
                    lastEvent = e;
                    const chartContainer = document.querySelector('.price-chart-container');
                    const chartRect = chartContainer.getBoundingClientRect();
                    let newLeft = lastEvent.clientX - dragOffsetX - chartRect.left;
                    let newTop = lastEvent.clientY - dragOffsetY - chartRect.top;
                    newLeft = Math.max(0, Math.min(newLeft, chartRect.width - wrapper.offsetWidth));
                    newTop = Math.max(0, Math.min(newTop, chartRect.height - wrapper.offsetHeight));
                    wrapper.style.left = newLeft + 'px';
                    wrapper.style.top = newTop + 'px';
                }
                function onMouseUp() {
                    isDragging = false;
                    document.removeEventListener('mousemove', onMouseMove);
                    document.removeEventListener('mouseup', onMouseUp);
                }
            })();
            // Resize logic (reuse popup chart logic)
            (function() {
                const wrapper = rrCalcPopup;
                const resizeHandle = document.getElementById('rr-calc-popup-resize');
                let isResizing = false, startX = 0, startY = 0, startWidth = 0, startHeight = 0;
                resizeHandle.addEventListener('mousedown', function(e) {
                    isResizing = true;
                    startX = e.clientX;
                    startY = e.clientY;
                    startWidth = wrapper.offsetWidth;
                    startHeight = wrapper.offsetHeight;
                    document.addEventListener('mousemove', onResizeMove);
                    document.addEventListener('mouseup', onResizeUp);
                    e.preventDefault();
                });
                function onResizeMove(e) {
                    if (!isResizing) return;
                    let newWidth = startWidth + (e.clientX - startX);
                    let newHeight = startHeight + (e.clientY - startY);
                    newWidth = Math.max(300, Math.min(newWidth, 600));
                    newHeight = Math.max(200, Math.min(newHeight, 600));
                    wrapper.style.width = newWidth + 'px';
                    wrapper.style.height = newHeight + 'px';
                }
                function onResizeUp() {
                    isResizing = false;
                    document.removeEventListener('mousemove', onResizeMove);
                    document.removeEventListener('mouseup', onResizeUp);
                }
            })();
        });
        </script>
    </body>
</html>
