/**
 * Type Definitions and JSDoc Types for PeckerSocket 7.0
 * Provides comprehensive type definitions for better code documentation and IDE support
 */

/**
 * @fileoverview Type definitions for the PeckerSocket trading dashboard
 * <AUTHOR> Team
 * @version 7.0
 */

// ============================================================================
// Core Data Types
// ============================================================================

/**
 * @typedef {Object} PriceData
 * @property {number} timestamp - Unix timestamp in milliseconds
 * @property {number} open - Opening price
 * @property {number} high - Highest price
 * @property {number} low - Lowest price
 * @property {number} close - Closing price
 * @property {number} volume - Trading volume
 */

/**
 * @typedef {Object} TradeData
 * @property {number} timestamp - Unix timestamp in milliseconds
 * @property {number} price - Trade price
 * @property {number} amount - Trade amount/volume
 * @property {'buy'|'sell'} side - Trade side
 * @property {string} id - Unique trade identifier
 */

/**
 * @typedef {Object} OrderBookEntry
 * @property {number} price - Price level
 * @property {number} amount - Amount at this price level
 */

/**
 * @typedef {Object} OrderBookData
 * @property {OrderBookEntry[]} bids - Array of bid entries
 * @property {OrderBookEntry[]} asks - Array of ask entries
 * @property {number} timestamp - Unix timestamp in milliseconds
 */

/**
 * @typedef {Object} CVDData
 * @property {number} timestamp - Unix timestamp in milliseconds
 * @property {number} cvd - Cumulative Volume Delta value
 * @property {number} volume - Total volume
 * @property {number} buyVolume - Buy volume
 * @property {number} sellVolume - Sell volume
 */

// ============================================================================
// WebSocket Types
// ============================================================================

/**
 * @typedef {Object} WebSocketConfig
 * @property {number} [reconnectDelay=2000] - Delay between reconnection attempts in ms
 * @property {number} [maxReconnectAttempts=10] - Maximum number of reconnection attempts
 * @property {number} [pingInterval=30000] - Ping interval in ms
 * @property {boolean} [autoReconnect=true] - Whether to automatically reconnect
 */

/**
 * @typedef {Object} WebSocketStats
 * @property {boolean} connected - Connection status
 * @property {boolean} connecting - Whether currently connecting
 * @property {number} reconnectAttempts - Number of reconnection attempts
 * @property {number} subscriptions - Number of active subscriptions
 * @property {number} handlers - Number of registered handlers
 * @property {number} lastMessageTime - Last message timestamp
 * @property {Object} messageStats - Message processing statistics
 */

/**
 * @typedef {Object} MessageStats
 * @property {number} processed - Number of processed messages
 * @property {number} queued - Number of queued messages
 * @property {number} dropped - Number of dropped messages
 * @property {number} averageProcessingTime - Average processing time in ms
 */

// ============================================================================
// Chart Types
// ============================================================================

/**
 * @typedef {Object} ChartConfig
 * @property {string} container - Container element ID
 * @property {number} width - Chart width
 * @property {number} height - Chart height
 * @property {Object} layout - Chart layout configuration
 * @property {Object} grid - Grid configuration
 * @property {Object} timeScale - Time scale configuration
 * @property {Object} priceScale - Price scale configuration
 */

/**
 * @typedef {Object} ChartSeries
 * @property {string} type - Series type (candlestick, line, histogram, etc.)
 * @property {Object} options - Series options
 * @property {Function} setData - Function to set series data
 * @property {Function} update - Function to update series data
 */

/**
 * @typedef {Object} ChartState
 * @property {string} symbol - Trading symbol
 * @property {string} timeframe - Chart timeframe
 * @property {PriceData[]} data - Chart data
 * @property {Object} indicators - Active indicators
 * @property {Object} settings - Chart settings
 */

// ============================================================================
// Indicator Types
// ============================================================================

/**
 * @typedef {Object} IndicatorConfig
 * @property {string} name - Indicator name
 * @property {Object} parameters - Indicator parameters
 * @property {string} [color] - Indicator color
 * @property {boolean} [visible=true] - Whether indicator is visible
 */

/**
 * @typedef {Object} IndicatorData
 * @property {number} timestamp - Unix timestamp in milliseconds
 * @property {number} value - Indicator value
 * @property {Object} [metadata] - Additional metadata
 */

// ============================================================================
// Performance Types
// ============================================================================

/**
 * @typedef {Object} PerformanceMetrics
 * @property {number} averageProcessingTime - Average processing time in ms
 * @property {number} slowestProcessingTime - Slowest processing time in ms
 * @property {number} fastestProcessingTime - Fastest processing time in ms
 * @property {number} totalOperations - Total number of operations
 * @property {number} errorCount - Number of errors
 */

/**
 * @typedef {Object} CacheStats
 * @property {number} hits - Cache hits
 * @property {number} misses - Cache misses
 * @property {number} size - Current cache size
 * @property {string} hitRate - Hit rate percentage
 */

/**
 * @typedef {Object} DOMCacheStats
 * @property {number} hits - DOM cache hits
 * @property {number} misses - DOM cache misses
 * @property {number} cacheSize - Current cache size
 * @property {number} selectorCacheSize - Selector cache size
 * @property {string} hitRate - Hit rate percentage
 * @property {string} selectorHitRate - Selector hit rate percentage
 */

// ============================================================================
// Event Types
// ============================================================================

/**
 * @typedef {Object} EventManagerStats
 * @property {string} name - Manager name
 * @property {number} added - Number of listeners added
 * @property {number} removed - Number of listeners removed
 * @property {number} aborted - Number of listeners aborted
 * @property {number} activeListeners - Number of active listeners
 * @property {boolean} isAborted - Whether manager is aborted
 */

/**
 * @typedef {Object} EventListenerConfig
 * @property {EventTarget} target - Event target
 * @property {string} type - Event type
 * @property {Function} listener - Event listener function
 * @property {Object} [options] - Event listener options
 */

// ============================================================================
// Error Handling Types
// ============================================================================

/**
 * @typedef {Object} ErrorContext
 * @property {string} source - Error source component
 * @property {string} [component] - Specific component
 * @property {string} [operation] - Operation being performed
 * @property {'error'|'warning'|'info'} [severity='error'] - Error severity
 * @property {Object} [context] - Additional context data
 */

/**
 * @typedef {Object} ErrorStats
 * @property {number} totalErrors - Total number of errors
 * @property {number} handledErrors - Number of handled errors
 * @property {number} unhandledErrors - Number of unhandled errors
 * @property {Object} errorsBySource - Errors grouped by source
 * @property {Object} errorsBySeverity - Errors grouped by severity
 */

// ============================================================================
// Configuration Types
// ============================================================================

/**
 * @typedef {Object} AppConfig
 * @property {Object} websockets - WebSocket configuration
 * @property {Object} charts - Chart configuration
 * @property {Object} ui - UI configuration
 * @property {Object} performance - Performance configuration
 * @property {Object} console - Console configuration
 */

/**
 * @typedef {Object} PerformanceConfig
 * @property {Object} domCache - DOM cache configuration
 * @property {Object} memoization - Memoization configuration
 * @property {Object} messageProcessing - Message processing configuration
 * @property {Object} eventManagement - Event management configuration
 */

// ============================================================================
// Utility Types
// ============================================================================

/**
 * @typedef {Object} ThrottleOptions
 * @property {boolean} [leading=true] - Execute on leading edge
 * @property {boolean} [trailing=true] - Execute on trailing edge
 */

/**
 * @typedef {Object} DebounceOptions
 * @property {boolean} [immediate=false] - Execute immediately
 */

/**
 * @typedef {Object} MemoizeOptions
 * @property {number} [maxSize=100] - Maximum cache size
 * @property {number} [ttl=300000] - Time to live in ms
 * @property {Function} [keyGenerator] - Custom key generator function
 * @property {Function} [onHit] - Cache hit callback
 * @property {Function} [onMiss] - Cache miss callback
 */

// ============================================================================
// API Types
// ============================================================================

/**
 * @typedef {Object} APIResponse
 * @property {boolean} success - Whether request was successful
 * @property {*} data - Response data
 * @property {string} [error] - Error message if unsuccessful
 * @property {number} timestamp - Response timestamp
 */

/**
 * @typedef {Object} APIConfig
 * @property {string} baseURL - Base API URL
 * @property {number} timeout - Request timeout in ms
 * @property {Object} headers - Default headers
 * @property {number} retries - Number of retry attempts
 */

// ============================================================================
// Export types for global access
// ============================================================================

if (typeof window !== 'undefined') {
  window.PeckerSocketTypes = {
    // Re-export all typedef names for runtime access
    typeNames: [
      'PriceData', 'TradeData', 'OrderBookEntry', 'OrderBookData', 'CVDData',
      'WebSocketConfig', 'WebSocketStats', 'MessageStats',
      'ChartConfig', 'ChartSeries', 'ChartState',
      'IndicatorConfig', 'IndicatorData',
      'PerformanceMetrics', 'CacheStats', 'DOMCacheStats',
      'EventManagerStats', 'EventListenerConfig',
      'ErrorContext', 'ErrorStats',
      'AppConfig', 'PerformanceConfig',
      'ThrottleOptions', 'DebounceOptions', 'MemoizeOptions',
      'APIResponse', 'APIConfig'
    ]
  };
}
