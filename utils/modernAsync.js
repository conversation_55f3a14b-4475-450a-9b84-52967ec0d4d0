/**
 * Modern Async Utilities
 * Provides modern async/await patterns and utilities to replace callback-based code
 */

(function() {
  'use strict';

  const logger = window.logger ? window.logger.createLogger('ModernAsync') : console;

  /**
   * Promise-based timeout utility
   * @param {number} ms - Milliseconds to wait
   * @returns {Promise<void>} Promise that resolves after timeout
   */
  const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

  /**
   * Promise-based timeout with value
   * @param {number} ms - Milliseconds to wait
   * @param {*} value - Value to resolve with
   * @returns {Promise<*>} Promise that resolves with value after timeout
   */
  const delay = (ms, value) => new Promise(resolve => setTimeout(() => resolve(value), ms));

  /**
   * Promise with timeout
   * @param {Promise} promise - Promise to wrap
   * @param {number} timeoutMs - Timeout in milliseconds
   * @param {string} timeoutMessage - Timeout error message
   * @returns {Promise<*>} Promise that rejects if timeout is reached
   */
  const withTimeout = (promise, timeoutMs, timeoutMessage = 'Operation timed out') => {
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error(timeoutMessage)), timeoutMs);
    });
    
    return Promise.race([promise, timeoutPromise]);
  };

  /**
   * Retry a promise-returning function with exponential backoff
   * @param {Function} fn - Function that returns a promise
   * @param {Object} options - Retry options
   * @returns {Promise<*>} Promise that resolves with the result
   */
  const retry = async (fn, options = {}) => {
    const {
      maxAttempts = 3,
      baseDelay = 1000,
      maxDelay = 10000,
      backoffFactor = 2,
      jitter = true,
      retryCondition = () => true
    } = options;

    let lastError;
    
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error;
        
        if (attempt === maxAttempts || !retryCondition(error, attempt)) {
          throw error;
        }

        // Calculate delay with exponential backoff
        let delayMs = Math.min(baseDelay * Math.pow(backoffFactor, attempt - 1), maxDelay);
        
        // Add jitter to prevent thundering herd
        if (jitter) {
          delayMs = delayMs * (0.5 + Math.random() * 0.5);
        }

        logger.debug(`Retry attempt ${attempt} failed, retrying in ${delayMs}ms:`, error.message);
        await sleep(delayMs);
      }
    }

    throw lastError;
  };

  /**
   * Execute promises in parallel with concurrency limit
   * @param {Array} items - Array of items to process
   * @param {Function} fn - Function that takes an item and returns a promise
   * @param {number} concurrency - Maximum concurrent operations
   * @returns {Promise<Array>} Promise that resolves with array of results
   */
  const parallelLimit = async (items, fn, concurrency = 5) => {
    const results = new Array(items.length);
    const executing = [];

    for (let i = 0; i < items.length; i++) {
      const promise = fn(items[i], i).then(result => {
        results[i] = result;
      });

      executing.push(promise);

      if (executing.length >= concurrency) {
        await Promise.race(executing);
        executing.splice(executing.findIndex(p => p === promise), 1);
      }
    }

    await Promise.all(executing);
    return results;
  };

  /**
   * Execute promises in series (one after another)
   * @param {Array} items - Array of items to process
   * @param {Function} fn - Function that takes an item and returns a promise
   * @returns {Promise<Array>} Promise that resolves with array of results
   */
  const series = async (items, fn) => {
    const results = [];
    for (let i = 0; i < items.length; i++) {
      results.push(await fn(items[i], i));
    }
    return results;
  };

  /**
   * Create a debounced async function
   * @param {Function} fn - Async function to debounce
   * @param {number} delay - Debounce delay in ms
   * @returns {Function} Debounced function
   */
  const debounceAsync = (fn, delay) => {
    let timeoutId;
    let pendingPromise;

    return (...args) => {
      return new Promise((resolve, reject) => {
        clearTimeout(timeoutId);
        
        timeoutId = setTimeout(async () => {
          try {
            if (pendingPromise) {
              await pendingPromise;
            }
            pendingPromise = fn(...args);
            const result = await pendingPromise;
            pendingPromise = null;
            resolve(result);
          } catch (error) {
            pendingPromise = null;
            reject(error);
          }
        }, delay);
      });
    };
  };

  /**
   * Create a throttled async function
   * @param {Function} fn - Async function to throttle
   * @param {number} delay - Throttle delay in ms
   * @returns {Function} Throttled function
   */
  const throttleAsync = (fn, delay) => {
    let lastExecution = 0;
    let pendingPromise;

    return async (...args) => {
      const now = Date.now();
      
      if (now - lastExecution >= delay) {
        lastExecution = now;
        if (pendingPromise) {
          await pendingPromise;
        }
        pendingPromise = fn(...args);
        const result = await pendingPromise;
        pendingPromise = null;
        return result;
      } else {
        // Return the pending promise if one exists
        if (pendingPromise) {
          return pendingPromise;
        }
        
        // Wait for the remaining time and then execute
        const remainingTime = delay - (now - lastExecution);
        await sleep(remainingTime);
        return throttleAsync(fn, delay)(...args);
      }
    };
  };

  /**
   * Create a circuit breaker for async operations
   * @param {Function} fn - Async function to wrap
   * @param {Object} options - Circuit breaker options
   * @returns {Function} Circuit breaker wrapped function
   */
  const circuitBreaker = (fn, options = {}) => {
    const {
      failureThreshold = 5,
      resetTimeout = 60000,
      monitoringPeriod = 60000
    } = options;

    let state = 'CLOSED'; // CLOSED, OPEN, HALF_OPEN
    let failures = 0;
    let lastFailureTime = 0;
    let successCount = 0;

    return async (...args) => {
      const now = Date.now();

      // Reset failure count if monitoring period has passed
      if (now - lastFailureTime > monitoringPeriod) {
        failures = 0;
      }

      // Check circuit state
      if (state === 'OPEN') {
        if (now - lastFailureTime > resetTimeout) {
          state = 'HALF_OPEN';
          successCount = 0;
        } else {
          throw new Error('Circuit breaker is OPEN');
        }
      }

      try {
        const result = await fn(...args);
        
        // Success handling
        if (state === 'HALF_OPEN') {
          successCount++;
          if (successCount >= 3) {
            state = 'CLOSED';
            failures = 0;
          }
        }
        
        return result;
      } catch (error) {
        failures++;
        lastFailureTime = now;
        
        if (failures >= failureThreshold) {
          state = 'OPEN';
        } else if (state === 'HALF_OPEN') {
          state = 'OPEN';
        }
        
        throw error;
      }
    };
  };

  /**
   * Convert callback-based function to promise
   * @param {Function} fn - Callback-based function
   * @returns {Function} Promise-returning function
   */
  const promisify = (fn) => {
    return (...args) => {
      return new Promise((resolve, reject) => {
        fn(...args, (error, result) => {
          if (error) {
            reject(error);
          } else {
            resolve(result);
          }
        });
      });
    };
  };

  /**
   * Create an async queue for processing items sequentially
   * @param {Function} processor - Function to process each item
   * @param {number} concurrency - Number of concurrent processors
   * @returns {Object} Queue object with push method
   */
  const createAsyncQueue = (processor, concurrency = 1) => {
    const queue = [];
    let running = 0;
    let paused = false;

    const processNext = async () => {
      if (paused || running >= concurrency || queue.length === 0) {
        return;
      }

      running++;
      const { item, resolve, reject } = queue.shift();

      try {
        const result = await processor(item);
        resolve(result);
      } catch (error) {
        reject(error);
      } finally {
        running--;
        processNext();
      }
    };

    return {
      push: (item) => {
        return new Promise((resolve, reject) => {
          queue.push({ item, resolve, reject });
          processNext();
        });
      },
      
      pause: () => {
        paused = true;
      },
      
      resume: () => {
        paused = false;
        processNext();
      },
      
      clear: () => {
        queue.length = 0;
      },
      
      size: () => queue.length,
      
      running: () => running
    };
  };

  // Export utilities
  window.ModernAsync = {
    sleep,
    delay,
    withTimeout,
    retry,
    parallelLimit,
    series,
    debounceAsync,
    throttleAsync,
    circuitBreaker,
    promisify,
    createAsyncQueue
  };

  // Also export individual functions for convenience
  Object.assign(window, {
    sleep,
    delay,
    withTimeout,
    retry,
    parallelLimit,
    series,
    debounceAsync,
    throttleAsync,
    circuitBreaker,
    promisify,
    createAsyncQueue
  });

})();
