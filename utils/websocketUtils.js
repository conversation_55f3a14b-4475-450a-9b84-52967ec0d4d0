/**
 * WebSocket Utilities
 * Common patterns and utilities for WebSocket management to reduce code duplication
 */

(function() {
  'use strict';

  const logger = window.logger ? window.logger.createLogger('WebSocketUtils') : console;

  /**
   * WebSocket Factory for creating standardized WebSocket managers
   */
  class WebSocketFactory {
    static createManager(url, exchange, options = {}) {
      const defaultOptions = {
        reconnectDelay: 2000,
        maxReconnectAttempts: 10,
        pingInterval: 30000,
        ...options
      };

      try {
        if (!window.WebSocketManager) {
          throw new Error('WebSocketManager class not available');
        }

        const manager = new window.WebSocketManager(url, exchange, defaultOptions);
        
        // Add standard error handling
        manager.on = manager.on || function(event, callback) {
          // Fallback event handling if not implemented
          logger.warn(`Event handling not implemented for ${event}`);
        };

        return manager;
      } catch (error) {
        if (window.errorHandler) {
          window.errorHandler.handleError(error, {
            source: 'websocketUtils',
            component: 'factory',
            operation: 'createManager',
            context: { url, exchange, options }
          });
        } else {
          logger.error(`Failed to create WebSocket manager for ${exchange}:`, error);
        }
        return null;
      }
    }

    /**
     * Initialize standard WebSocket managers for the application
     */
    static initializeStandardManagers() {
      const managers = {};

      // Bitstamp WebSocket
      if (!window.bitstampWsManager) {
        managers.bitstamp = this.createManager(
          'wss://ws.bitstamp.net',
          'bitstamp',
          { reconnectDelay: 2000 }
        );
        if (managers.bitstamp) {
          window.bitstampWsManager = managers.bitstamp;
        }
      }

      // Bybit WebSocket
      if (!window.bybitWsManager) {
        managers.bybit = this.createManager(
          'wss://stream.bybit.com/v5/public/linear',
          'bybit',
          { reconnectDelay: 2000 }
        );
        if (managers.bybit) {
          window.bybitWsManager = managers.bybit;
        }
      }

      return managers;
    }

    /**
     * Create WebSocket manager with enhanced error handling and monitoring
     */
    static createEnhancedManager(url, exchange, options = {}) {
      const manager = this.createManager(url, exchange, options);
      
      if (!manager) return null;

      // Add performance monitoring
      const originalProcessMessage = manager.processMessage;
      if (originalProcessMessage) {
        manager.processMessage = function(data) {
          const startTime = performance.now();
          try {
            const result = originalProcessMessage.call(this, data);
            const processingTime = performance.now() - startTime;
            
            // Log slow message processing
            if (processingTime > 5) {
              logger.warn(`Slow message processing detected: ${processingTime.toFixed(2)}ms for ${exchange}`);
            }
            
            return result;
          } catch (error) {
            if (window.errorHandler) {
              window.errorHandler.handleError(error, {
                source: 'websocketUtils',
                component: 'enhancedManager',
                operation: 'processMessage',
                context: { exchange, processingTime: performance.now() - startTime }
              });
            }
            throw error;
          }
        };
      }

      return manager;
    }
  }

  /**
   * WebSocket Connection Pool for managing multiple connections
   */
  class WebSocketPool {
    constructor() {
      this.connections = new Map();
      this.stats = {
        totalConnections: 0,
        activeConnections: 0,
        failedConnections: 0
      };
    }

    /**
     * Add a WebSocket manager to the pool
     */
    addConnection(name, manager) {
      if (this.connections.has(name)) {
        logger.warn(`Connection ${name} already exists in pool`);
        return false;
      }

      this.connections.set(name, {
        manager,
        created: Date.now(),
        lastActivity: Date.now()
      });

      this.stats.totalConnections++;
      this.updateActiveConnections();
      
      return true;
    }

    /**
     * Get a WebSocket manager from the pool
     */
    getConnection(name) {
      const connection = this.connections.get(name);
      if (connection) {
        connection.lastActivity = Date.now();
        return connection.manager;
      }
      return null;
    }

    /**
     * Remove a connection from the pool
     */
    removeConnection(name) {
      const connection = this.connections.get(name);
      if (connection) {
        try {
          if (connection.manager.disconnect) {
            connection.manager.disconnect();
          }
        } catch (error) {
          logger.error(`Error disconnecting ${name}:`, error);
        }
        
        this.connections.delete(name);
        this.updateActiveConnections();
        return true;
      }
      return false;
    }

    /**
     * Update active connections count
     */
    updateActiveConnections() {
      this.stats.activeConnections = 0;
      this.stats.failedConnections = 0;

      for (const [name, connection] of this.connections.entries()) {
        if (connection.manager.connected) {
          this.stats.activeConnections++;
        } else {
          this.stats.failedConnections++;
        }
      }
    }

    /**
     * Get pool statistics
     */
    getStats() {
      this.updateActiveConnections();
      return {
        ...this.stats,
        connections: Array.from(this.connections.keys()),
        poolSize: this.connections.size
      };
    }

    /**
     * Cleanup all connections
     */
    cleanup() {
      for (const [name, connection] of this.connections.entries()) {
        try {
          if (connection.manager.disconnect) {
            connection.manager.disconnect();
          }
        } catch (error) {
          logger.error(`Error cleaning up connection ${name}:`, error);
        }
      }
      this.connections.clear();
      this.stats = {
        totalConnections: 0,
        activeConnections: 0,
        failedConnections: 0
      };
    }
  }

  /**
   * WebSocket Message Router for handling message routing between different handlers
   */
  class MessageRouter {
    constructor() {
      this.routes = new Map();
      this.middleware = [];
      this.stats = {
        messagesRouted: 0,
        routingErrors: 0,
        averageRoutingTime: 0,
        routingTimes: []
      };
    }

    /**
     * Add a route for message handling
     */
    addRoute(pattern, handler) {
      if (typeof pattern === 'string') {
        this.routes.set(pattern, handler);
      } else if (pattern instanceof RegExp) {
        this.routes.set(pattern, handler);
      } else {
        throw new Error('Route pattern must be string or RegExp');
      }
    }

    /**
     * Add middleware for message processing
     */
    use(middleware) {
      if (typeof middleware !== 'function') {
        throw new Error('Middleware must be a function');
      }
      this.middleware.push(middleware);
    }

    /**
     * Route a message to appropriate handlers
     */
    route(message, context = {}) {
      const startTime = performance.now();
      
      try {
        // Apply middleware
        let processedMessage = message;
        for (const middleware of this.middleware) {
          processedMessage = middleware(processedMessage, context) || processedMessage;
        }

        // Find matching routes
        let routed = false;
        for (const [pattern, handler] of this.routes.entries()) {
          let matches = false;
          
          if (typeof pattern === 'string') {
            matches = processedMessage.type === pattern || processedMessage.channel === pattern;
          } else if (pattern instanceof RegExp) {
            matches = pattern.test(JSON.stringify(processedMessage));
          }

          if (matches) {
            try {
              handler(processedMessage, context);
              routed = true;
            } catch (error) {
              this.stats.routingErrors++;
              if (window.errorHandler) {
                window.errorHandler.handleError(error, {
                  source: 'websocketUtils',
                  component: 'messageRouter',
                  operation: 'route',
                  context: { pattern: pattern.toString(), message: processedMessage }
                });
              }
            }
          }
        }

        if (!routed) {
          logger.debug('No route found for message:', processedMessage);
        }

        this.stats.messagesRouted++;
        
        // Update performance stats
        const routingTime = performance.now() - startTime;
        this.stats.routingTimes.push(routingTime);
        
        if (this.stats.routingTimes.length > 100) {
          this.stats.routingTimes.shift();
        }
        
        this.stats.averageRoutingTime = 
          this.stats.routingTimes.reduce((a, b) => a + b, 0) / this.stats.routingTimes.length;

      } catch (error) {
        this.stats.routingErrors++;
        if (window.errorHandler) {
          window.errorHandler.handleError(error, {
            source: 'websocketUtils',
            component: 'messageRouter',
            operation: 'route'
          });
        }
      }
    }

    /**
     * Get routing statistics
     */
    getStats() {
      return {
        ...this.stats,
        routeCount: this.routes.size,
        middlewareCount: this.middleware.length
      };
    }
  }

  // Create global instances
  const wsPool = new WebSocketPool();
  const messageRouter = new MessageRouter();

  // Cleanup on page unload
  window.addEventListener('beforeunload', () => {
    wsPool.cleanup();
  });

  // Export to global scope
  window.WebSocketFactory = WebSocketFactory;
  window.webSocketPool = wsPool;
  window.messageRouter = messageRouter;

  // Convenience functions
  window.createWebSocketManager = (url, exchange, options) => 
    WebSocketFactory.createManager(url, exchange, options);
  
  window.initializeWebSockets = () => 
    WebSocketFactory.initializeStandardManagers();

})();
