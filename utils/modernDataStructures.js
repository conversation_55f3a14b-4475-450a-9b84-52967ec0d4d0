/**
 * Modern Data Structures and Utilities
 * Provides optimized data structures and modern JavaScript patterns
 */

(function() {
  'use strict';

  const logger = window.logger ? window.logger.createLogger('ModernDataStructures') : console;

  /**
   * Circular Buffer for efficient fixed-size collections
   */
  class CircularBuffer {
    constructor(capacity) {
      this.capacity = capacity;
      this.buffer = new Array(capacity);
      this.head = 0;
      this.tail = 0;
      this.size = 0;
    }

    /**
     * Add item to buffer
     * @param {*} item - Item to add
     * @returns {*} Removed item if buffer was full
     */
    push(item) {
      let removed = null;
      
      if (this.size === this.capacity) {
        removed = this.buffer[this.tail];
        this.tail = (this.tail + 1) % this.capacity;
      } else {
        this.size++;
      }

      this.buffer[this.head] = item;
      this.head = (this.head + 1) % this.capacity;
      
      return removed;
    }

    /**
     * Get item at index
     * @param {number} index - Index (0 is oldest)
     * @returns {*} Item at index
     */
    get(index) {
      if (index >= this.size) return undefined;
      return this.buffer[(this.tail + index) % this.capacity];
    }

    /**
     * Get the most recent item
     * @returns {*} Most recent item
     */
    latest() {
      if (this.size === 0) return undefined;
      return this.buffer[(this.head - 1 + this.capacity) % this.capacity];
    }

    /**
     * Get the oldest item
     * @returns {*} Oldest item
     */
    oldest() {
      if (this.size === 0) return undefined;
      return this.buffer[this.tail];
    }

    /**
     * Convert to array
     * @returns {Array} Array representation
     */
    toArray() {
      const result = new Array(this.size);
      for (let i = 0; i < this.size; i++) {
        result[i] = this.get(i);
      }
      return result;
    }

    /**
     * Clear the buffer
     */
    clear() {
      this.head = 0;
      this.tail = 0;
      this.size = 0;
    }

    /**
     * Check if buffer is full
     * @returns {boolean} Whether buffer is full
     */
    isFull() {
      return this.size === this.capacity;
    }

    /**
     * Check if buffer is empty
     * @returns {boolean} Whether buffer is empty
     */
    isEmpty() {
      return this.size === 0;
    }
  }

  /**
   * Time Series data structure optimized for financial data
   */
  class TimeSeries {
    constructor(maxSize = 10000) {
      this.maxSize = maxSize;
      this.data = [];
      this.index = new Map(); // timestamp -> array index for fast lookups
    }

    /**
     * Add data point
     * @param {number} timestamp - Unix timestamp
     * @param {*} value - Data value
     */
    add(timestamp, value) {
      // Remove oldest if at capacity
      if (this.data.length >= this.maxSize) {
        const oldest = this.data.shift();
        this.index.delete(oldest.timestamp);
      }

      const dataPoint = { timestamp, value };
      this.data.push(dataPoint);
      this.index.set(timestamp, this.data.length - 1);
    }

    /**
     * Get data point by timestamp
     * @param {number} timestamp - Unix timestamp
     * @returns {*} Data point or undefined
     */
    get(timestamp) {
      const index = this.index.get(timestamp);
      return index !== undefined ? this.data[index] : undefined;
    }

    /**
     * Get data points in time range
     * @param {number} startTime - Start timestamp
     * @param {number} endTime - End timestamp
     * @returns {Array} Array of data points
     */
    getRange(startTime, endTime) {
      return this.data.filter(point => 
        point.timestamp >= startTime && point.timestamp <= endTime
      );
    }

    /**
     * Get latest N data points
     * @param {number} count - Number of points to get
     * @returns {Array} Array of latest data points
     */
    getLatest(count) {
      return this.data.slice(-count);
    }

    /**
     * Get all data points
     * @returns {Array} All data points
     */
    getAll() {
      return [...this.data];
    }

    /**
     * Clear all data
     */
    clear() {
      this.data = [];
      this.index.clear();
    }

    /**
     * Get size
     * @returns {number} Number of data points
     */
    size() {
      return this.data.length;
    }

    /**
     * Calculate moving average
     * @param {number} window - Window size
     * @returns {Array} Array of moving averages
     */
    movingAverage(window) {
      if (this.data.length < window) return [];
      
      const result = [];
      for (let i = window - 1; i < this.data.length; i++) {
        const sum = this.data.slice(i - window + 1, i + 1)
          .reduce((acc, point) => acc + (typeof point.value === 'number' ? point.value : 0), 0);
        result.push({
          timestamp: this.data[i].timestamp,
          value: sum / window
        });
      }
      return result;
    }
  }

  /**
   * Priority Queue implementation
   */
  class PriorityQueue {
    constructor(compareFn = (a, b) => a.priority - b.priority) {
      this.heap = [];
      this.compare = compareFn;
    }

    /**
     * Add item to queue
     * @param {*} item - Item to add
     * @param {number} priority - Item priority
     */
    enqueue(item, priority) {
      const node = { item, priority };
      this.heap.push(node);
      this.bubbleUp(this.heap.length - 1);
    }

    /**
     * Remove and return highest priority item
     * @returns {*} Highest priority item
     */
    dequeue() {
      if (this.heap.length === 0) return undefined;
      
      const max = this.heap[0];
      const end = this.heap.pop();
      
      if (this.heap.length > 0) {
        this.heap[0] = end;
        this.bubbleDown(0);
      }
      
      return max.item;
    }

    /**
     * Peek at highest priority item without removing
     * @returns {*} Highest priority item
     */
    peek() {
      return this.heap.length > 0 ? this.heap[0].item : undefined;
    }

    /**
     * Check if queue is empty
     * @returns {boolean} Whether queue is empty
     */
    isEmpty() {
      return this.heap.length === 0;
    }

    /**
     * Get queue size
     * @returns {number} Queue size
     */
    size() {
      return this.heap.length;
    }

    /**
     * Bubble up element at index
     * @private
     */
    bubbleUp(index) {
      while (index > 0) {
        const parentIndex = Math.floor((index - 1) / 2);
        if (this.compare(this.heap[index], this.heap[parentIndex]) >= 0) break;
        
        [this.heap[index], this.heap[parentIndex]] = [this.heap[parentIndex], this.heap[index]];
        index = parentIndex;
      }
    }

    /**
     * Bubble down element at index
     * @private
     */
    bubbleDown(index) {
      while (true) {
        let minIndex = index;
        const leftChild = 2 * index + 1;
        const rightChild = 2 * index + 2;

        if (leftChild < this.heap.length && 
            this.compare(this.heap[leftChild], this.heap[minIndex]) < 0) {
          minIndex = leftChild;
        }

        if (rightChild < this.heap.length && 
            this.compare(this.heap[rightChild], this.heap[minIndex]) < 0) {
          minIndex = rightChild;
        }

        if (minIndex === index) break;

        [this.heap[index], this.heap[minIndex]] = [this.heap[minIndex], this.heap[index]];
        index = minIndex;
      }
    }
  }

  /**
   * Efficient Set operations
   */
  class FastSet extends Set {
    /**
     * Union with another set
     * @param {Set} other - Other set
     * @returns {FastSet} New set with union
     */
    union(other) {
      const result = new FastSet(this);
      for (const item of other) {
        result.add(item);
      }
      return result;
    }

    /**
     * Intersection with another set
     * @param {Set} other - Other set
     * @returns {FastSet} New set with intersection
     */
    intersection(other) {
      const result = new FastSet();
      for (const item of this) {
        if (other.has(item)) {
          result.add(item);
        }
      }
      return result;
    }

    /**
     * Difference with another set
     * @param {Set} other - Other set
     * @returns {FastSet} New set with difference
     */
    difference(other) {
      const result = new FastSet();
      for (const item of this) {
        if (!other.has(item)) {
          result.add(item);
        }
      }
      return result;
    }

    /**
     * Check if this set is a subset of another
     * @param {Set} other - Other set
     * @returns {boolean} Whether this is a subset
     */
    isSubsetOf(other) {
      for (const item of this) {
        if (!other.has(item)) {
          return false;
        }
      }
      return true;
    }

    /**
     * Convert to array
     * @returns {Array} Array representation
     */
    toArray() {
      return Array.from(this);
    }
  }

  /**
   * Efficient Map with additional utilities
   */
  class FastMap extends Map {
    /**
     * Get value with default
     * @param {*} key - Map key
     * @param {*} defaultValue - Default value
     * @returns {*} Value or default
     */
    getOrDefault(key, defaultValue) {
      return this.has(key) ? this.get(key) : defaultValue;
    }

    /**
     * Set if key doesn't exist
     * @param {*} key - Map key
     * @param {*} value - Map value
     * @returns {boolean} Whether value was set
     */
    setIfAbsent(key, value) {
      if (!this.has(key)) {
        this.set(key, value);
        return true;
      }
      return false;
    }

    /**
     * Compute value if absent
     * @param {*} key - Map key
     * @param {Function} computeFn - Function to compute value
     * @returns {*} Existing or computed value
     */
    computeIfAbsent(key, computeFn) {
      if (!this.has(key)) {
        this.set(key, computeFn(key));
      }
      return this.get(key);
    }

    /**
     * Filter map entries
     * @param {Function} predicate - Filter predicate
     * @returns {FastMap} New filtered map
     */
    filter(predicate) {
      const result = new FastMap();
      for (const [key, value] of this) {
        if (predicate(value, key, this)) {
          result.set(key, value);
        }
      }
      return result;
    }

    /**
     * Map values to new map
     * @param {Function} mapper - Value mapper function
     * @returns {FastMap} New mapped map
     */
    mapValues(mapper) {
      const result = new FastMap();
      for (const [key, value] of this) {
        result.set(key, mapper(value, key, this));
      }
      return result;
    }

    /**
     * Convert to object
     * @returns {Object} Object representation
     */
    toObject() {
      const obj = {};
      for (const [key, value] of this) {
        obj[key] = value;
      }
      return obj;
    }
  }

  // Export data structures
  window.ModernDataStructures = {
    CircularBuffer,
    TimeSeries,
    PriorityQueue,
    FastSet,
    FastMap
  };

  // Also export classes individually for convenience
  Object.assign(window, {
    CircularBuffer,
    TimeSeries,
    PriorityQueue,
    FastSet,
    FastMap
  });

})();
