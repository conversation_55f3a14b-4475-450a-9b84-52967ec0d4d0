/**
 * Advanced Memoization Utility
 * Provides efficient caching for expensive function calls with TTL, LRU eviction, and performance monitoring
 */

(function() {
  'use strict';

  const logger = window.logger ? window.logger.createLogger('Memoization') : console;

  /**
   * LRU Cache with TTL support
   */
  class LRUCache {
    constructor(maxSize = 100, ttl = 300000) { // 5 minutes default TTL
      this.maxSize = maxSize;
      this.ttl = ttl;
      this.cache = new Map();
      this.accessOrder = new Map(); // Track access order for LRU
      this.stats = {
        hits: 0,
        misses: 0,
        evictions: 0,
        expired: 0
      };
    }

    get(key) {
      const entry = this.cache.get(key);
      
      if (!entry) {
        this.stats.misses++;
        return undefined;
      }

      // Check if expired
      if (Date.now() - entry.timestamp > this.ttl) {
        this.cache.delete(key);
        this.accessOrder.delete(key);
        this.stats.expired++;
        this.stats.misses++;
        return undefined;
      }

      // Update access order
      this.accessOrder.delete(key);
      this.accessOrder.set(key, Date.now());
      
      this.stats.hits++;
      return entry.value;
    }

    set(key, value) {
      // Remove if already exists
      if (this.cache.has(key)) {
        this.cache.delete(key);
        this.accessOrder.delete(key);
      }

      // Evict least recently used if at capacity
      if (this.cache.size >= this.maxSize) {
        const lruKey = this.accessOrder.keys().next().value;
        this.cache.delete(lruKey);
        this.accessOrder.delete(lruKey);
        this.stats.evictions++;
      }

      // Add new entry
      this.cache.set(key, {
        value,
        timestamp: Date.now()
      });
      this.accessOrder.set(key, Date.now());
    }

    clear() {
      this.cache.clear();
      this.accessOrder.clear();
      this.stats = { hits: 0, misses: 0, evictions: 0, expired: 0 };
    }

    getStats() {
      const total = this.stats.hits + this.stats.misses;
      return {
        ...this.stats,
        size: this.cache.size,
        hitRate: total > 0 ? (this.stats.hits / total * 100).toFixed(2) + '%' : '0%'
      };
    }
  }

  /**
   * Advanced memoization function with configurable options
   */
  function memoize(fn, options = {}) {
    const {
      maxSize = 100,
      ttl = 300000, // 5 minutes
      keyGenerator = (...args) => JSON.stringify(args),
      onHit = null,
      onMiss = null,
      onEviction = null
    } = options;

    const cache = new LRUCache(maxSize, ttl);
    const fnName = fn.name || 'anonymous';

    const memoized = function(...args) {
      const key = keyGenerator(...args);
      
      // Try to get from cache
      const cached = cache.get(key);
      if (cached !== undefined) {
        if (onHit) onHit(key, cached);
        return cached;
      }

      // Cache miss - compute value
      if (onMiss) onMiss(key);
      const result = fn.apply(this, args);
      
      // Store in cache
      cache.set(key, result);
      
      return result;
    };

    // Add utility methods
    memoized.cache = cache;
    memoized.clear = () => cache.clear();
    memoized.getStats = () => ({
      function: fnName,
      ...cache.getStats()
    });
    memoized.delete = (key) => {
      cache.cache.delete(key);
      cache.accessOrder.delete(key);
    };

    return memoized;
  }

  /**
   * Memoize with weak references for object keys
   */
  function memoizeWeak(fn, options = {}) {
    const cache = new WeakMap();
    const stats = { hits: 0, misses: 0 };

    return function(...args) {
      // Use first argument as key if it's an object
      const key = args[0];
      if (typeof key !== 'object' || key === null) {
        throw new Error('memoizeWeak requires first argument to be an object');
      }

      if (cache.has(key)) {
        stats.hits++;
        return cache.get(key);
      }

      stats.misses++;
      const result = fn.apply(this, args);
      cache.set(key, result);
      return result;
    };
  }

  /**
   * Memoize expensive calculations with automatic cleanup
   */
  function memoizeCalculation(fn, options = {}) {
    const {
      maxSize = 50,
      ttl = 60000, // 1 minute for calculations
      cleanupInterval = 300000 // 5 minutes
    } = options;

    const memoized = memoize(fn, { maxSize, ttl });
    
    // Periodic cleanup of expired entries
    const cleanupTimer = setInterval(() => {
      const oldSize = memoized.cache.cache.size;
      const now = Date.now();
      
      for (const [key, entry] of memoized.cache.cache.entries()) {
        if (now - entry.timestamp > ttl) {
          memoized.cache.cache.delete(key);
          memoized.cache.accessOrder.delete(key);
        }
      }
      
      const newSize = memoized.cache.cache.size;
      if (oldSize !== newSize) {
        logger.debug(`Cleaned up ${oldSize - newSize} expired calculation cache entries`);
      }
    }, cleanupInterval);

    // Cleanup timer when memoized function is no longer needed
    memoized.destroy = () => {
      clearInterval(cleanupTimer);
      memoized.clear();
    };

    return memoized;
  }

  /**
   * Global memoization registry for managing all memoized functions
   */
  class MemoizationRegistry {
    constructor() {
      this.functions = new Map();
    }

    register(name, memoizedFn) {
      this.functions.set(name, memoizedFn);
    }

    unregister(name) {
      const fn = this.functions.get(name);
      if (fn && fn.destroy) {
        fn.destroy();
      }
      this.functions.delete(name);
    }

    getStats() {
      const stats = {};
      for (const [name, fn] of this.functions.entries()) {
        if (fn.getStats) {
          stats[name] = fn.getStats();
        }
      }
      return stats;
    }

    clearAll() {
      for (const [name, fn] of this.functions.entries()) {
        if (fn.clear) {
          fn.clear();
        }
      }
    }

    destroy() {
      for (const [name, fn] of this.functions.entries()) {
        if (fn.destroy) {
          fn.destroy();
        }
      }
      this.functions.clear();
    }
  }

  // Create global registry
  const registry = new MemoizationRegistry();

  // Cleanup on page unload
  window.addEventListener('beforeunload', () => {
    registry.destroy();
  });

  // Export to global scope
  window.memoize = memoize;
  window.memoizeWeak = memoizeWeak;
  window.memoizeCalculation = memoizeCalculation;
  window.memoizationRegistry = registry;

  // Utility functions for common use cases
  window.memoizeUtils = {
    // Memoize array operations
    memoizeArrayOp: (fn) => memoize(fn, {
      keyGenerator: (arr, ...args) => `${arr.length}-${JSON.stringify(args)}-${arr[0]}-${arr[arr.length-1]}`,
      maxSize: 50,
      ttl: 30000 // 30 seconds for array operations
    }),

    // Memoize DOM queries
    memoizeDOMQuery: (fn) => memoize(fn, {
      maxSize: 200,
      ttl: 60000, // 1 minute for DOM queries
      keyGenerator: (...args) => args.join('|')
    }),

    // Memoize API responses
    memoizeAPI: (fn) => memoize(fn, {
      maxSize: 100,
      ttl: 300000, // 5 minutes for API responses
      onHit: (key) => logger.debug(`API cache hit: ${key}`),
      onMiss: (key) => logger.debug(`API cache miss: ${key}`)
    })
  };

})();
