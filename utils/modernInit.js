/**
 * Modern Initialization System
 * Demonstrates modern JavaScript patterns and initializes the enhanced utilities
 */

(function() {
  'use strict';

  const logger = window.logger ? window.logger.createLogger('ModernInit') : console;

  /**
   * Initialize all modern utilities and demonstrate usage patterns
   */
  async function initializeModernSystems() {
    try {
      logger.info('Initializing modern systems...');

      // Initialize module system
      await initializeModuleSystem();

      // Initialize performance monitoring
      await initializePerformanceMonitoring();

      // Initialize modern WebSocket management
      await initializeModernWebSockets();

      // Initialize modern data structures for trading data
      await initializeTradingDataStructures();

      // Initialize modern event management
      await initializeEventManagement();

      logger.info('Modern systems initialized successfully');
      
      // Emit initialization complete event
      if (window.ModuleSystem?.events) {
        window.ModuleSystem.events.emit('modern-systems-initialized');
      }

    } catch (error) {
      if (window.errorHandler) {
        window.errorHandler.handleError(error, {
          source: 'modernInit',
          component: 'initialization',
          operation: 'initializeModernSystems'
        });
      } else {
        logger.error('Failed to initialize modern systems:', error);
      }
    }
  }

  /**
   * Initialize the module system with core modules
   */
  async function initializeModuleSystem() {
    if (!window.ModuleSystem) {
      throw new Error('ModuleSystem not available');
    }

    // Register core services
    window.ModuleSystem.service('logger', window.logger);
    window.ModuleSystem.service('errorHandler', window.errorHandler);
    window.ModuleSystem.service('config', window.CONFIG);
    window.ModuleSystem.service('domCache', window.domCache);

    // Register performance utilities
    if (window.memoizationRegistry) {
      window.ModuleSystem.service('memoization', window.memoizationRegistry);
    }

    if (window.eventManagerRegistry) {
      window.ModuleSystem.service('eventManagers', window.eventManagerRegistry);
    }

    // Register WebSocket utilities
    if (window.webSocketPool) {
      window.ModuleSystem.service('webSocketPool', window.webSocketPool);
    }

    logger.debug('Module system initialized with core services');
  }

  /**
   * Initialize performance monitoring
   */
  async function initializePerformanceMonitoring() {
    // Create performance monitoring event manager
    const perfEventManager = window.getEventManager('performance');

    // Monitor DOM cache performance
    if (window.domCache?.getStats) {
      setInterval(() => {
        const stats = window.domCache.getStats();
        if (parseFloat(stats.hitRate) < 80) {
          logger.warn('DOM cache hit rate below 80%:', stats);
        }
      }, 30000); // Check every 30 seconds
    }

    // Monitor memoization performance
    if (window.memoizationRegistry?.getStats) {
      setInterval(() => {
        const stats = window.memoizationRegistry.getStats();
        Object.entries(stats).forEach(([name, fnStats]) => {
          if (parseFloat(fnStats.hitRate) < 70) {
            logger.warn(`Memoization hit rate below 70% for ${name}:`, fnStats);
          }
        });
      }, 60000); // Check every minute
    }

    // Monitor WebSocket performance
    if (window.webSocketPool?.getStats) {
      setInterval(() => {
        const stats = window.webSocketPool.getStats();
        if (stats.failedConnections > 0) {
          logger.warn('WebSocket connection failures detected:', stats);
        }
      }, 15000); // Check every 15 seconds
    }

    logger.debug('Performance monitoring initialized');
  }

  /**
   * Initialize modern WebSocket management
   */
  async function initializeModernWebSockets() {
    if (!window.WebSocketFactory) {
      logger.warn('WebSocketFactory not available, skipping modern WebSocket initialization');
      return;
    }

    try {
      // Initialize standard managers using the factory
      const managers = window.WebSocketFactory.initializeStandardManagers();
      
      // Add enhanced error handling and performance monitoring
      Object.entries(managers).forEach(([name, manager]) => {
        if (manager) {
          // Add to pool for monitoring
          window.webSocketPool?.addConnection(name, manager);
          
          // Add performance monitoring
          if (manager.getPerformanceStats) {
            setInterval(() => {
              const stats = manager.getPerformanceStats();
              if (stats.messageStats.averageProcessingTime > 5) {
                logger.warn(`Slow message processing detected for ${name}:`, stats);
              }
            }, 30000);
          }
        }
      });

      logger.debug('Modern WebSocket management initialized');
    } catch (error) {
      logger.error('Failed to initialize modern WebSocket management:', error);
    }
  }

  /**
   * Initialize modern data structures for trading data
   */
  async function initializeTradingDataStructures() {
    if (!window.ModernDataStructures) {
      logger.warn('ModernDataStructures not available');
      return;
    }

    // Create global trading data stores using modern data structures
    window.TradingData = {
      // Price data with circular buffers for efficiency
      priceBuffers: new Map(),
      
      // Time series for historical data
      timeSeries: new Map(),
      
      // Priority queue for order processing
      orderQueue: new window.PriorityQueue((a, b) => a.timestamp - b.timestamp),
      
      // Fast sets for active symbols
      activeSymbols: new window.FastSet(),
      
      // Fast maps for quick lookups
      symbolData: new window.FastMap()
    };

    // Initialize price buffers for common symbols
    const symbols = ['BTC', 'ETH', 'ADA', 'SOL', 'DOGE'];
    symbols.forEach(symbol => {
      window.TradingData.priceBuffers.set(symbol, new window.CircularBuffer(1000));
      window.TradingData.timeSeries.set(symbol, new window.TimeSeries(10000));
      window.TradingData.activeSymbols.add(symbol);
    });

    // Create memoized functions for expensive calculations
    const memoizedCalculations = {
      movingAverage: window.memoizeCalculation((data, period) => {
        if (data.length < period) return [];
        const result = [];
        for (let i = period - 1; i < data.length; i++) {
          const sum = data.slice(i - period + 1, i + 1).reduce((a, b) => a + b.value, 0);
          result.push({ timestamp: data[i].timestamp, value: sum / period });
        }
        return result;
      }),

      volatility: window.memoizeCalculation((data, period) => {
        if (data.length < period) return 0;
        const prices = data.slice(-period).map(d => d.value);
        const mean = prices.reduce((a, b) => a + b, 0) / prices.length;
        const variance = prices.reduce((a, b) => a + Math.pow(b - mean, 2), 0) / prices.length;
        return Math.sqrt(variance);
      })
    };

    window.TradingCalculations = memoizedCalculations;

    logger.debug('Trading data structures initialized');
  }

  /**
   * Initialize modern event management
   */
  async function initializeEventManagement() {
    if (!window.EventManager) {
      logger.warn('EventManager not available');
      return;
    }

    // Create specialized event managers for different components
    const chartEventManager = window.getEventManager('charts');
    const wsEventManager = window.getEventManager('websockets');
    const uiEventManager = window.getEventManager('ui');

    // Set up global event listeners with automatic cleanup
    uiEventManager.addEventListener(window, 'resize', 
      window.commonUtils?.debounce(() => {
        window.ModuleSystem?.events?.emit('window-resized');
      }, 250) || (() => {}), 
      { passive: true }
    );

    uiEventManager.addEventListener(document, 'visibilitychange', () => {
      const isVisible = !document.hidden;
      window.ModuleSystem?.events?.emit('visibility-changed', isVisible);
      
      // Pause/resume performance monitoring based on visibility
      if (isVisible) {
        logger.debug('Page became visible, resuming full monitoring');
      } else {
        logger.debug('Page became hidden, reducing monitoring frequency');
      }
    });

    // Set up error boundary for unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      if (window.errorHandler) {
        window.errorHandler.handleError(event.reason, {
          source: 'modernInit',
          component: 'unhandledRejection',
          operation: 'global_error_handler'
        });
      }
      
      // Prevent default browser error handling
      event.preventDefault();
    });

    logger.debug('Modern event management initialized');
  }

  /**
   * Create performance dashboard for monitoring
   */
  function createPerformanceDashboard() {
    // Only create in development or when explicitly enabled
    if (window.CONFIG?.performance?.showDashboard !== true) {
      return;
    }

    const dashboard = document.createElement('div');
    dashboard.id = 'performance-dashboard';
    dashboard.style.cssText = `
      position: fixed;
      top: 10px;
      right: 10px;
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 10px;
      border-radius: 5px;
      font-family: monospace;
      font-size: 12px;
      z-index: 10000;
      max-width: 300px;
    `;

    const updateDashboard = () => {
      const stats = {
        domCache: window.domCache?.getStats?.() || {},
        memoization: Object.keys(window.memoizationRegistry?.getStats?.() || {}).length,
        webSockets: window.webSocketPool?.getStats?.() || {},
        eventManagers: Object.keys(window.eventManagerRegistry?.getStats?.() || {}).length
      };

      dashboard.innerHTML = `
        <h4>Performance Dashboard</h4>
        <div>DOM Cache: ${stats.domCache.hitRate || '0%'} hit rate</div>
        <div>Memoized Functions: ${stats.memoization}</div>
        <div>WebSocket Connections: ${stats.webSockets.activeConnections || 0}</div>
        <div>Event Managers: ${stats.eventManagers}</div>
        <div>Memory: ${(performance.memory?.usedJSHeapSize / 1024 / 1024).toFixed(1) || 'N/A'} MB</div>
      `;
    };

    document.body.appendChild(dashboard);
    setInterval(updateDashboard, 5000);
    updateDashboard();
  }

  // Initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeModernSystems);
  } else {
    // DOM is already ready
    setTimeout(initializeModernSystems, 0);
  }

  // Create performance dashboard if enabled
  setTimeout(createPerformanceDashboard, 1000);

  // Export for manual initialization if needed
  window.initializeModernSystems = initializeModernSystems;

})();
