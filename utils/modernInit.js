/**
 * Modern Initialization System
 * Demonstrates modern JavaScript patterns and initializes the enhanced utilities
 */

(function() {
  'use strict';

  const logger = window.logger ? window.logger.createLogger('ModernInit') : console;

  /**
   * Initialize all modern utilities and demonstrate usage patterns
   */
  async function initializeModernSystems() {
    try {
      logger.info('Initializing modern systems...');

      // Initialize module system
      await initializeModuleSystem();

      // Initialize performance monitoring
      await initializePerformanceMonitoring();

      // Initialize modern WebSocket management
      await initializeModernWebSockets();

      // Initialize modern data structures for trading data
      await initializeTradingDataStructures();

      // Initialize modern event management
      await initializeEventManagement();

      logger.info('Modern systems initialized successfully');
      
      // Emit initialization complete event
      if (window.ModuleSystem?.events) {
        window.ModuleSystem.events.emit('modern-systems-initialized');
      }

    } catch (error) {
      if (window.errorHandler) {
        window.errorHandler.handleError(error, {
          source: 'modernInit',
          component: 'initialization',
          operation: 'initializeModernSystems'
        });
      } else {
        logger.error('Failed to initialize modern systems:', error);
      }
    }
  }

  /**
   * Initialize the module system with core modules
   */
  async function initializeModuleSystem() {
    if (!window.ModuleSystem) {
      throw new Error('ModuleSystem not available');
    }

    // Register core services
    window.ModuleSystem.service('logger', window.logger);
    window.ModuleSystem.service('errorHandler', window.errorHandler);
    window.ModuleSystem.service('config', window.CONFIG);
    window.ModuleSystem.service('domCache', window.domCache);

    // Register performance utilities
    if (window.memoizationRegistry) {
      window.ModuleSystem.service('memoization', window.memoizationRegistry);
    }

    if (window.eventManagerRegistry) {
      window.ModuleSystem.service('eventManagers', window.eventManagerRegistry);
    }

    // Register WebSocket utilities
    if (window.webSocketPool) {
      window.ModuleSystem.service('webSocketPool', window.webSocketPool);
    }

    logger.debug('Module system initialized with core services');
  }

  /**
   * Initialize performance monitoring with optimized intervals
   */
  async function initializePerformanceMonitoring() {
    // Create performance monitoring event manager
    const perfEventManager = window.getEventManager ? window.getEventManager('performance') : null;

    // Use requestIdleCallback for performance monitoring when available
    const scheduleMonitoring = (callback, fallbackDelay) => {
      if (window.requestIdleCallback) {
        const monitor = () => {
          requestIdleCallback(() => {
            callback();
            setTimeout(monitor, fallbackDelay);
          }, { timeout: 1000 });
        };
        monitor();
      } else {
        setInterval(callback, fallbackDelay);
      }
    };

    // Monitor DOM cache performance (less frequent)
    if (window.domCache?.getStats) {
      scheduleMonitoring(() => {
        try {
          const stats = window.domCache.getStats();
          if (parseFloat(stats.hitRate) < 80) {
            logger.warn('DOM cache hit rate below 80%:', stats);
          }
        } catch (error) {
          // Silently handle errors in monitoring
        }
      }, 45000); // Check every 45 seconds
    }

    // Monitor memoization performance (less frequent)
    if (window.memoizationRegistry?.getStats) {
      scheduleMonitoring(() => {
        try {
          const stats = window.memoizationRegistry.getStats();
          Object.entries(stats).forEach(([name, fnStats]) => {
            if (parseFloat(fnStats.hitRate) < 70) {
              logger.warn(`Memoization hit rate below 70% for ${name}:`, fnStats);
            }
          });
        } catch (error) {
          // Silently handle errors in monitoring
        }
      }, 90000); // Check every 90 seconds
    }

    // Monitor WebSocket performance (less frequent)
    if (window.webSocketPool?.getStats) {
      scheduleMonitoring(() => {
        try {
          const stats = window.webSocketPool.getStats();
          if (stats.failedConnections > 0) {
            logger.warn('WebSocket connection failures detected:', stats);
          }
        } catch (error) {
          // Silently handle errors in monitoring
        }
      }, 30000); // Check every 30 seconds
    }

    logger.debug('Performance monitoring initialized with optimized intervals');
  }

  /**
   * Initialize modern WebSocket management
   */
  async function initializeModernWebSockets() {
    if (!window.WebSocketFactory) {
      logger.warn('WebSocketFactory not available, skipping modern WebSocket initialization');
      return;
    }

    // Check if WebSocketManager class is available
    if (!window.WebSocketManager) {
      logger.debug('WebSocketManager class not yet available, will initialize later');

      // Set up a listener to initialize when WebSocketManager becomes available
      const checkForWebSocketManager = () => {
        if (window.WebSocketManager) {
          logger.debug('WebSocketManager now available, initializing modern WebSocket management');
          initializeWebSocketManagers();
        } else {
          // Check again in 100ms
          setTimeout(checkForWebSocketManager, 100);
        }
      };

      setTimeout(checkForWebSocketManager, 100);
      return;
    }

    await initializeWebSocketManagers();
  }

  /**
   * Initialize WebSocket managers once WebSocketManager class is available
   */
  async function initializeWebSocketManagers() {
    try {
      // Initialize standard managers using the factory
      const managers = window.WebSocketFactory.initializeStandardManagers();

      // Add enhanced error handling and performance monitoring
      Object.entries(managers).forEach(([name, manager]) => {
        if (manager) {
          // Add to pool for monitoring
          window.webSocketPool?.addConnection(name, manager);

          // Add performance monitoring with throttling to prevent spam
          if (manager.getPerformanceStats) {
            const monitorPerformance = window.throttleAsync ?
              window.throttleAsync(() => {
                const stats = manager.getPerformanceStats();
                if (stats.messageStats.averageProcessingTime > 5) {
                  logger.warn(`Slow message processing detected for ${name}:`, stats);
                }
              }, 30000) :
              () => {}; // Fallback if throttleAsync not available

            setInterval(monitorPerformance, 30000);
          }
        }
      });

      logger.debug('Modern WebSocket management initialized');
    } catch (error) {
      logger.error('Failed to initialize modern WebSocket management:', error);
    }
  }

  /**
   * Initialize modern data structures for trading data
   */
  async function initializeTradingDataStructures() {
    if (!window.ModernDataStructures) {
      logger.warn('ModernDataStructures not available');
      return;
    }

    // Create global trading data stores using modern data structures
    window.TradingData = {
      // Price data with circular buffers for efficiency
      priceBuffers: new Map(),
      
      // Time series for historical data
      timeSeries: new Map(),
      
      // Priority queue for order processing
      orderQueue: new window.PriorityQueue((a, b) => a.timestamp - b.timestamp),
      
      // Fast sets for active symbols
      activeSymbols: new window.FastSet(),
      
      // Fast maps for quick lookups
      symbolData: new window.FastMap()
    };

    // Initialize price buffers for common symbols
    const symbols = ['BTC', 'ETH', 'ADA', 'SOL', 'DOGE'];
    symbols.forEach(symbol => {
      window.TradingData.priceBuffers.set(symbol, new window.CircularBuffer(1000));
      window.TradingData.timeSeries.set(symbol, new window.TimeSeries(10000));
      window.TradingData.activeSymbols.add(symbol);
    });

    // Create memoized functions for expensive calculations
    const memoizedCalculations = {
      movingAverage: window.memoizeCalculation((data, period) => {
        if (data.length < period) return [];
        const result = [];
        for (let i = period - 1; i < data.length; i++) {
          const sum = data.slice(i - period + 1, i + 1).reduce((a, b) => a + b.value, 0);
          result.push({ timestamp: data[i].timestamp, value: sum / period });
        }
        return result;
      }),

      volatility: window.memoizeCalculation((data, period) => {
        if (data.length < period) return 0;
        const prices = data.slice(-period).map(d => d.value);
        const mean = prices.reduce((a, b) => a + b, 0) / prices.length;
        const variance = prices.reduce((a, b) => a + Math.pow(b - mean, 2), 0) / prices.length;
        return Math.sqrt(variance);
      })
    };

    window.TradingCalculations = memoizedCalculations;

    logger.debug('Trading data structures initialized');
  }

  /**
   * Initialize modern event management
   */
  async function initializeEventManagement() {
    if (!window.EventManager) {
      logger.warn('EventManager not available');
      return;
    }

    // Create specialized event managers for different components
    const chartEventManager = window.getEventManager('charts');
    const wsEventManager = window.getEventManager('websockets');
    const uiEventManager = window.getEventManager('ui');

    // Set up global event listeners with automatic cleanup
    uiEventManager.addEventListener(window, 'resize', 
      window.commonUtils?.debounce(() => {
        window.ModuleSystem?.events?.emit('window-resized');
      }, 250) || (() => {}), 
      { passive: true }
    );

    uiEventManager.addEventListener(document, 'visibilitychange', () => {
      const isVisible = !document.hidden;
      window.ModuleSystem?.events?.emit('visibility-changed', isVisible);
      
      // Pause/resume performance monitoring based on visibility
      if (isVisible) {
        logger.debug('Page became visible, resuming full monitoring');
      } else {
        logger.debug('Page became hidden, reducing monitoring frequency');
      }
    });

    // Set up error boundary for unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      if (window.errorHandler) {
        window.errorHandler.handleError(event.reason, {
          source: 'modernInit',
          component: 'unhandledRejection',
          operation: 'global_error_handler'
        });
      }
      
      // Prevent default browser error handling
      event.preventDefault();
    });

    logger.debug('Modern event management initialized');
  }

  /**
   * Create performance dashboard for monitoring (optimized)
   */
  function createPerformanceDashboard() {
    // Only create in development or when explicitly enabled
    if (window.CONFIG?.performance?.showDashboard !== true) {
      return;
    }

    const dashboard = document.createElement('div');
    dashboard.id = 'performance-dashboard';
    dashboard.style.cssText = `
      position: fixed;
      top: 10px;
      right: 10px;
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 10px;
      border-radius: 5px;
      font-family: monospace;
      font-size: 12px;
      z-index: 10000;
      max-width: 300px;
      cursor: pointer;
    `;

    let isVisible = true;
    let updateInterval;

    const updateDashboard = () => {
      if (!isVisible || document.hidden) return;

      try {
        const stats = {
          domCache: window.domCache?.getStats?.() || {},
          memoization: Object.keys(window.memoizationRegistry?.getStats?.() || {}).length,
          webSockets: window.webSocketPool?.getStats?.() || {},
          eventManagers: Object.keys(window.eventManagerRegistry?.getStats?.() || {}).length
        };

        // Use requestAnimationFrame for smooth updates
        requestAnimationFrame(() => {
          dashboard.innerHTML = `
            <h4>Performance Dashboard</h4>
            <div>DOM Cache: ${stats.domCache.hitRate || '0%'} hit rate</div>
            <div>Memoized Functions: ${stats.memoization}</div>
            <div>WebSocket Connections: ${stats.webSockets.activeConnections || 0}</div>
            <div>Event Managers: ${stats.eventManagers}</div>
            <div>Memory: ${(performance.memory?.usedJSHeapSize / 1024 / 1024).toFixed(1) || 'N/A'} MB</div>
            <div style="font-size: 10px; margin-top: 5px; opacity: 0.7;">Click to toggle</div>
          `;
        });
      } catch (error) {
        // Silently handle errors
      }
    };

    // Toggle visibility on click
    dashboard.addEventListener('click', () => {
      isVisible = !isVisible;
      dashboard.style.opacity = isVisible ? '1' : '0.3';
      if (isVisible) {
        updateDashboard();
      }
    });

    // Pause updates when page is hidden
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        clearInterval(updateInterval);
      } else {
        updateInterval = setInterval(updateDashboard, 10000); // Slower updates
        updateDashboard();
      }
    });

    document.body.appendChild(dashboard);
    updateInterval = setInterval(updateDashboard, 10000); // Update every 10 seconds
    updateDashboard();
  }

  // Initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeModernSystems);
  } else {
    // DOM is already ready
    setTimeout(initializeModernSystems, 0);
  }

  // Create performance dashboard if enabled
  setTimeout(createPerformanceDashboard, 1000);

  // Export for manual initialization if needed
  window.initializeModernSystems = initializeModernSystems;

})();
