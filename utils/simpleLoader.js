(function() {
    'use strict';

    // Simple, dependency-free loading manager
    const SimpleLoader = {
        progress: 0,
        isLoading: true,
        loadedScripts: new Set(),
        progressBar: null,
        statusText: null,

        init() {
            this.startTime = Date.now();
            this.createProgressUI();
            this.startLoading();
        },

        createProgressUI() {
            // Create simple progress bar
            this.progressBar = document.createElement('div');
            this.progressBar.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 0%;
                height: 2px;
                background: linear-gradient(90deg, #26a69a, #4db6ac);
                z-index: 10001;
                transition: width 0.3s ease-out;
                box-shadow: 0 0 5px rgba(38, 166, 154, 0.3);
                opacity: 0.7;
            `;

            // Create status indicator
            this.statusText = document.createElement('div');
            this.statusText.style.cssText = `
                position: fixed;
                bottom: 20px;
                right: 20px;
                background: rgba(0, 0, 0, 0.6);
                color: #26a69a;
                padding: 6px 10px;
                border-radius: 4px;
                font-size: 0.75rem;
                z-index: 10001;
                opacity: 0.6;
                backdrop-filter: blur(2px);
            `;
            this.statusText.textContent = 'Loading...';

            document.body.appendChild(this.progressBar);
            document.body.appendChild(this.statusText);
        },

        updateProgress(percent, message) {
            this.progress = Math.min(100, percent);
            if (this.progressBar) {
                this.progressBar.style.width = this.progress + '%';
            }
            if (this.statusText && message) {
                this.statusText.textContent = message;
            }

            if (this.progress >= 100) {
                setTimeout(() => this.complete(), 200);
            }
        },

        loadScript(src) {
            return new Promise((resolve, reject) => {
                if (this.loadedScripts.has(src)) {
                    resolve();
                    return;
                }

                const script = document.createElement('script');
                script.src = src;
                script.async = false; // Maintain order

                script.onload = () => {
                    this.loadedScripts.add(src);
                    resolve();
                };

                script.onerror = () => {
                    console.warn('Failed to load:', src);
                    resolve(); // Continue even if script fails
                };

                document.head.appendChild(script);
            });
        },

        async loadLightweightCharts() {
            return new Promise((resolve, reject) => {
                // Check if already loaded
                if (window.LightweightCharts) {
                    resolve();
                    return;
                }

                // Load from CDN with retry mechanism (used as fallback)
                const loadFromCDN = (attempt = 1) => {
                    const script = document.createElement('script');
                    script.src = 'https://unpkg.com/lightweight-charts@5.0.0/dist/lightweight-charts.standalone.production.js';
                    script.async = false;

                    script.onload = () => {
                        this.loadedScripts.add(script.src);
                        resolve();
                    };

                    script.onerror = () => {
                        if (attempt < 3) {
                            console.warn(`Failed to load LightweightCharts from CDN (attempt ${attempt}), retrying...`);
                            setTimeout(() => loadFromCDN(attempt + 1), 1000);
                        } else {
                            // Try alternative CDN as fallback
                            const fallbackScript = document.createElement('script');
                            fallbackScript.src = 'https://cdn.jsdelivr.net/npm/lightweight-charts@5.0.0/dist/lightweight-charts.standalone.production.min.js';
                            fallbackScript.async = false;

                            fallbackScript.onload = () => {
                                this.loadedScripts.add(fallbackScript.src);
                                resolve();
                            };

                            fallbackScript.onerror = () => {
                                const error = new Error('Failed to load LightweightCharts from all CDNs');
                                if (window.errorHandler) {
                                    window.errorHandler.handleError(error, {
                                        source: 'simpleLoader',
                                        component: 'LightweightCharts',
                                        operation: 'CDN_fallback_load',
                                        severity: 'warning'
                                    });
                                } else {
                                    console.error('Failed to load LightweightCharts from all CDNs');
                                }
                                // Continue anyway to prevent blocking the app
                                resolve();
                            };

                            document.head.appendChild(fallbackScript);
                        }
                    };

                    document.head.appendChild(script);
                };

                // First, try to use the preloaded resources
                const preloadedLinks = Array.from(document.querySelectorAll('link[rel="preload"][as="script"]'))
                    .filter(link => link.href.includes('lightweight-charts'));

                if (preloadedLinks.length > 0) {
                    // Use the first preloaded resource
                    const script = document.createElement('script');
                    script.src = preloadedLinks[0].href;
                    script.async = false;

                    script.onload = () => {
                        this.loadedScripts.add(script.src);
                        resolve();
                    };

                    script.onerror = () => {
                        // If the preloaded resource fails, try the fallback
                        if (preloadedLinks.length > 1) {
                            const fallbackScript = document.createElement('script');
                            fallbackScript.src = preloadedLinks[1].href;
                            fallbackScript.async = false;

                            fallbackScript.onload = () => {
                                this.loadedScripts.add(fallbackScript.src);
                                resolve();
                            };

                            fallbackScript.onerror = () => {
                                console.error('Failed to load LightweightCharts from preloaded resources');
                                // Continue anyway to prevent blocking the app
                                resolve();
                            };

                            document.head.appendChild(fallbackScript);
                        } else {
                            // If no fallback preloaded resource, use the CDN directly
                            loadFromCDN();
                        }
                    };

                    document.head.appendChild(script);
                } else {
                    // If no preloaded resources, use the CDN directly
                    loadFromCDN();
                }
            });
        },

        async startLoading() {
            try {
                // Phase 1: Essential scripts and chart library
                this.updateProgress(10, 'Loading core utilities...');
                await Promise.all([
                    this.loadLightweightCharts(),
                    this.loadScript('utils/commonUtils.js'),
                    this.loadScript('utils/mathUtils.js'),
                    this.loadScript('utils/config.js'),
                    this.loadScript('utils/dataFetcher.js')
                ]);

                // Wait for core utilities to be available
                await this.waitForModule('utils', 5000);
                await this.waitForModule('mathUtils', 5000);

                // Phase 1.5: Create global variables that modules need
                this.updateProgress(15, 'Setting up global variables...');
                
                // Create global variables that modules depend on
                if (!window.domCache) {
                    window.domCache = new Map();
                }
                if (!window.chartStates) {
                    window.chartStates = new Map();
                }
                if (!window.currentPair) {
                    window.currentPair = "BTC";
                }
                if (!window.currentActivePair) {
                    window.currentActivePair = "BTC";
                }

                // Add database constants that dataFetcher needs
                if (!window.HIST_DB_NAME) {
                    window.HIST_DB_NAME = "crypto-dashboard-hist";
                }
                if (!window.HIST_DB_VERSION) {
                    window.HIST_DB_VERSION = 1;
                }
                if (!window.HIST_STORE) {
                    window.HIST_STORE = "bars";
                }
                if (!window.getHistDb) {
                    let histDb = null;
                    window.getHistDb = async function() {
                        if (histDb) return histDb;
                        if (window.IndexedDbWrapper) {
                            histDb = new window.IndexedDbWrapper(window.HIST_DB_NAME, window.HIST_DB_VERSION, {
                                [window.HIST_STORE]: { keyPath: "key" },
                            });
                            await histDb.ready;
                            return histDb;
                        }
                        throw new Error("IndexedDbWrapper not loaded");
                    };
                }

                // Phase 2: Load configManager first (provides CONFIG, PAIRS, utils)
                this.updateProgress(20, 'Loading configuration manager...');
                await this.loadScript('modules/charts/chartutilities/configManager.js');
                await this.waitForModule('configManager', 3000);

                // Phase 3: Load IndexedDbWrapper (required by dataFetcher)
                this.updateProgress(25, 'Loading database wrapper...');
                await this.loadScript('utils/db/indexedDbWrapper.js');
                await this.waitForModule('IndexedDbWrapper', 3000);

                // Phase 4: Load dataFetcher (depends on configManager and IndexedDbWrapper)
                this.updateProgress(30, 'Loading data fetcher...');
                await this.loadScript('modules/charts/chartutilities/dataFetcher.js');
                await this.waitForModule('dataFetcher', 3000);

                // Phase 5: Load other modules sequentially to ensure dependencies
                this.updateProgress(35, 'Loading chart modules...');
                
                // Load modules that depend on configManager and domCache
                await this.loadScript('modules/charts/chartutilities/chartInitializer.js');
                await this.waitForModule('chartInitializer', 3000);
                
                await this.loadScript('modules/charts/chartutilities/pairSwitcher.js');
                await this.waitForModule('pairSwitcher', 3000);
                
                await this.loadScript('modules/charts/chartutilities/settingsManager.js');
                await this.waitForModule('settingsManager', 3000);
                
                await this.loadScript('modules/charts/chartutilities/memoryManager.js');
                await this.waitForModule('memoryManager', 3000);
                
                await this.loadScript('modules/charts/chartutilities/performanceOptimizer.js');
                await this.waitForModule('performanceOptimizer', 3000);
                
                await this.loadScript('modules/charts/chartutilities/pollingManager.js');
                await this.waitForModule('pollingManager', 3000);

                // Load modules that depend on chartStates and currentPair
                await this.loadScript('modules/charts/chartutilities/websocketMessageHandler.js');
                await this.waitForModule('websocketMessageHandler', 3000);
                
                await this.loadScript('modules/charts/chartutilities/eventHandlers.js');
                await this.waitForModule('eventHandlers', 3000);

                // Phase 6: WebSocket manager
                this.updateProgress(45, 'Loading WebSocket manager...');
                await this.loadScript('wsmanager.js');

                // Phase 7: Core modules
                this.updateProgress(55, 'Loading orderbook module...');
                await this.loadScript('modules/orderbook.js');

                // Wait for LightweightCharts to be fully available
                await this.waitForModule('LightweightCharts', 10000);

                // Load data stores first (required by indicators)
                await this.loadScript('indicators/data/cvdDataStore.js');
                await this.loadScript('indicators/data/perpCvdDataStore.js');
                await this.loadScript('indicators/data/perpImbalanceDataStore.js');

                // CRITICAL FIX: Load indicator modules BEFORE charts.js
                this.updateProgress(60, 'Loading indicator modules...');
                await this.loadScript('indicators/utils.js');
                await this.waitForModule('utils', 3000);
                
                // Load shared utilities
                await this.loadScript('utils/shared/indicatorChartUtils.js');
                await this.loadScript('utils/shared/profiles/baseProfile.js');

                // Load indicator modules
                await this.loadScript('indicators/cvd.js');
                await this.loadScript('indicators/perpcvd.js');
                await this.loadScript('indicators/perpimbalance.js');
                await this.loadScript('indicators/indicators.js');

                // Wait for indicator modules to be available
                await this.waitForModule('cvdModule', 3000);
                await this.waitForModule('perpCvdModule', 3000);
                await this.waitForModule('chartIndicators', 3000);

                // Now load charts.js after all indicator modules are ready
                this.updateProgress(70, 'Loading chart modules...');
                await this.loadScript('modules/charts/charts.js');
                
                // Wait for charts.js to be loaded and trigger initialization
                await this.waitForModule('initializeChart', 5000);

                // Phase 8: Console and UI
                this.updateProgress(80, 'Loading UI components...');
                await this.loadScript('modules/consoleCapture.js');

                // Load utility managers
                await this.loadScript('utils/chartSwitcher.js');

                // CRITICAL FIX: Add small delay to ensure data stores are properly initialized
                await new Promise(resolve => setTimeout(resolve, 100));

                // Verify PERP CVD data store loaded correctly
                if (!window.PS || !window.PS.setPerpCVDPriceData) {
                    console.warn('[SimpleLoader] PERP CVD Data Store failed to load, retrying...');

                    if (!window.PS || !window.PS.setPerpCVDPriceData) {
                        console.error('[SimpleLoader] PERP CVD Data Store still not available after retry');
                    }
                } else {
                    // Check if we have existing chart data to inject
                    setTimeout(() => {
                        if (window.chartStates) {
                            const btcState = window.chartStates.get('BTC');
                            if (btcState && btcState.data && btcState.data.alignedBybit && btcState.data.alignedBybit.length > 0) {
                                window.PS.setPerpCVDPriceData(btcState.data.alignedBybit.slice());
                            }
                        }
                    }, 1000); // Wait 1 second for chart data to be available
                }

                // Load popup chart module
                await this.loadScript('modules/charts/popupChart/PopupChart.js');

                // Verify critical modules loaded
                await this.verifyModules();

                // Phase 9: Trigger chart initialization
                this.updateProgress(95, 'Starting chart initialization...');
                
                // Trigger chart initialization if available
                if (window.initializeChart) {
                    try {
                        await window.initializeChart();
                    } catch (error) {
                        if (window.errorHandler) {
                            window.errorHandler.handleError(error, {
                                source: 'simpleLoader',
                                component: 'chart',
                                operation: 'initialization',
                                severity: 'error'
                            });
                        } else {
                            console.error('[SimpleLoader] Chart initialization failed:', error);
                        }
                    }
                }
                
                // Initialize settings button after chart initialization
                setTimeout(() => {
                    if (window.settingsManager?.initSettingsButton) {
                        window.settingsManager.initSettingsButton();
                    }
                }, 1000);

                // Optimize WebSocket reconnection after a delay
                setTimeout(() => {
                    if (window.optimizeWebSocketReconnection) {
                        window.optimizeWebSocketReconnection();
                    }
                }, 2000);

                // Phase 10: Complete
                this.updateProgress(100, 'Dashboard ready!');
                
                // Add fallback trigger for chart initialization
                setTimeout(() => {
                    if (!window.chartStates || window.chartStates.size === 0) {
                        if (window.triggerChartInitialization) {
                            window.triggerChartInitialization();
                        }
                    }
                }, 3000);

            } catch (error) {
                if (window.errorHandler) {
                    window.errorHandler.handleError(error, {
                        source: 'simpleLoader',
                        component: 'main',
                        operation: 'loading',
                        severity: 'error'
                    });
                } else {
                    console.error('Loading error:', error);
                }
                this.updateProgress(100, 'Loading completed with errors');
            }
        },

        waitForModule(moduleName, timeout = 5000) {
            return new Promise((resolve) => {
                const startTime = Date.now();
                const checkModule = () => {
                    if (window[moduleName]) {
                        // Add a small delay to ensure the module is fully initialized
                        setTimeout(() => resolve(), 50);
                    } else if (Date.now() - startTime < timeout) {
                        setTimeout(checkModule, 100);
                    } else {
                        console.warn(`Module ${moduleName} not loaded within timeout`);

                        // For critical modules like LightweightCharts, try to load a fallback
                        if (moduleName === 'LightweightCharts') {
                            console.warn('Attempting to load fallback for LightweightCharts...');
                            const fallbackScript = document.createElement('script');
                            fallbackScript.src = 'https://cdn.jsdelivr.net/npm/lightweight-charts@5.0.0/dist/lightweight-charts.standalone.production.min.js';
                            fallbackScript.onload = () => {
                                console.log('Fallback LightweightCharts loaded successfully');
                                setTimeout(() => resolve(), 100);
                            };
                            fallbackScript.onerror = () => {
                                console.error('Failed to load fallback LightweightCharts');
                                resolve(); // Continue anyway
                            };
                            document.head.appendChild(fallbackScript);
                        } else {
                            resolve(); // Continue anyway for non-critical modules
                        }
                    }
                };
                checkModule();
            });
        },

        async verifyModules() {
            const requiredModules = ['LightweightCharts', 'cvdModule', 'perpCvdModule', 'perpImbalanceModule', 'IndicatorChartUtils', 'BaseProfile', 'popupChart', 'chartSwitcher', 'chartIndicators'];
            const requiredDataStoreFunctions = [
                'getCurrentPerpCVD', 
                'setPerpCVD', 'setPerpImbalancePriceData'
            ];
            const missingModules = [];
            const missingDataStoreFunctions = [];

            for (const module of requiredModules) {
                if (!window[module]) {
                    missingModules.push(module);
                }
            }

            for (const func of requiredDataStoreFunctions) {
                if (!window.PS || typeof window.PS[func] !== 'function') {
                    missingDataStoreFunctions.push(func);
                }
            }

            if (missingModules.length > 0) {
                console.warn('Missing modules:', missingModules);
                // Add stub modules to prevent errors
                this.addMissingModuleStubs(missingModules);
            }

            if (missingDataStoreFunctions.length > 0) {
                console.warn('Missing data store functions:', missingDataStoreFunctions);
            }

            // Always add general stubs for essential components
            this.addGeneralStubs();
        },

        addMissingModuleStubs(missingModules) {
            missingModules.forEach(moduleName => {
                if (moduleName === 'LightweightCharts') {
                    // Create a minimal LightweightCharts stub to prevent errors
                    window.LightweightCharts = {
                        createChart: (container, options) => ({
                            resize: () => {},
                            remove: () => {},
                            timeScale: () => ({
                                fitContent: () => {},
                                scrollToRealTime: () => {},
                                setVisibleRange: () => {},
                                getVisibleLogicalRange: () => ({ from: 0, to: 100 }),
                                subscribeVisibleLogicalRangeChange: (handler) => ({ remove: () => {} })
                            }),
                            addSeries: (seriesType, options) => ({
                                setData: () => {},
                                update: () => {},
                                createPriceLine: () => ({ applyOptions: () => {}, remove: () => {} }),
                                dataByIndex: () => []
                            }),
                            subscribeCrosshairMove: () => ({ remove: () => {} }),
                            applyOptions: () => {},
                            panes: () => [{}],
                            removeSeries: () => {}
                        }),
                        CandlestickSeries: 'candlestick',
                        LineSeries: 'line',
                        HistogramSeries: 'histogram',
                        CrosshairMode: { Normal: 'normal', Hidden: 'hidden' },
                        TrackingModeExitMode: { OnNextTap: 'on-next-tap' },
                        PriceLineSource: { LastBar: 'last-bar' }
                    };
                    console.warn('Created LightweightCharts stub - charts will not render properly');
                } else if (moduleName === 'cvdModule') {
                    window.cvdModule = {
                        createCVDChart: () => ({ series: null, syncResources: null }),
                        synchronizeCharts: () => ({}),
                        initializeCVDData: () => {},
                        updateCVD: () => {},
                        renderPendingCVDUpdates: () => {},
                        resizeCVDChart: () => {},
                        cleanupCVD: () => {}
                    };
                } else if (moduleName === 'perpCvdModule') {
                    window.perpCvdModule = {
                        createCVDChart: () => ({ series: null, syncResources: null }),
                        synchronizeCharts: () => ({}),
                        initializeCVDData: () => {},
                        updateCVD: () => {},
                        renderPendingCVDUpdates: () => {},
                        resizeCVDChart: () => {},
                        cleanupCVD: () => {}
                    };

                } else if (moduleName === 'perpImbalanceModule') {
                    window.perpImbalanceModule = {
                        createImbalanceChart: () => ({ series: null, syncResources: null }),
                        synchronizeCharts: () => ({}),
                        initializeImbalanceData: () => {},
                        renderPendingImbalanceUpdates: () => {},
                        resizeImbalanceChart: () => {},
                        cleanupImbalance: () => {}
                    };

                } else if (moduleName === 'chartIndicators') {
                    window.chartIndicators = {
                        calculateAllIndicators: (data) => ({
                            bands: [],
                            vwap: [],
                            vwapData: [],
                            emaBands: [],
                            caches: {}
                        }),
                        calculateLiqs: (bybitData, bitstampData, period) => ({
                            liqsData: [],
                            liqsRaw: [],
                            perpD: [],
                            spotD: []
                        }),
                        calculateEMABands: (data) => [],
                        calculateBands: (data) => [],
                        calculateCVD: (data) => [],
                        calculateVTWAP: (data, caches) => []
                    };

                } else if (moduleName === 'popupChart') {
                    window.popupChart = {
                        chart: null,
                        series: null,
                        initialize: () => {},
                        cleanup: () => {},
                        loadChartData: () => {},
                        updateTimeframe: () => {}
                    };
                }
            });
        },

        complete() {
            this.isLoading = false;

            // Initialize basic functionality
            this.initializeBasics();

            // Hide progress indicators quickly
            if (this.progressBar) {
                this.progressBar.style.opacity = '0';
                setTimeout(() => {
                    if (this.progressBar.parentNode) {
                        this.progressBar.parentNode.removeChild(this.progressBar);
                    }
                }, 150);
            }

            if (this.statusText) {
                this.statusText.style.opacity = '0';
                setTimeout(() => {
                    if (this.statusText.parentNode) {
                        this.statusText.parentNode.removeChild(this.statusText);
                    }
                }, 150);
            }

            // Mark as ready
            document.body.classList.add('dashboard-ready');

            // Dispatch ready event
            document.dispatchEvent(new CustomEvent('dashboardReady', {
                detail: { 
                    success: true, 
                    mode: 'simple',
                    loadingTime: Date.now() - (this.startTime || Date.now()),
                    modulesLoaded: {
                        cvdModule: !!window.cvdModule,
                        perpCvdModule: !!window.perpCvdModule,
                        perpImbalanceModule: !!window.perpImbalanceModule,
                    }
                }
            }));
        },

        initializeBasics() {
            // Set up basic chart states
            if (!window.chartStates) {
                window.chartStates = new Map();
            }
            if (!window.currentPair) {
                window.currentPair = 'BTC';
                window.currentActivePair = 'BTC';
            }

            // Initialize WebSocket connections if available
            if (window.WebSocketManager) {
                // Use WebSocket utilities for standardized initialization
                if (window.WebSocketFactory) {
                    try {
                        const managers = window.WebSocketFactory.initializeStandardManagers();

                        // Add managers to the pool for monitoring
                        if (window.webSocketPool) {
                            if (managers.bitstamp) {
                                window.webSocketPool.addConnection('bitstamp', managers.bitstamp);
                            }
                            if (managers.bybit) {
                                window.webSocketPool.addConnection('bybit', managers.bybit);
                            }
                        }
                    } catch (error) {
                        if (window.errorHandler) {
                            window.errorHandler.handleError(error, {
                                source: 'simpleLoader',
                                component: 'websocket',
                                operation: 'initialization'
                            });
                        } else {
                            console.warn('Failed to initialize WebSocket managers:', error);
                        }
                    }
                } else {
                    // Fallback to direct initialization if utilities not available
                    if (!window.bitstampWsManager) {
                        try {
                            window.bitstampWsManager = new WebSocketManager(
                                'wss://ws.bitstamp.net',
                                'bitstamp',
                                { reconnectDelay: 2000 }
                            );
                        } catch (error) {
                            console.warn('Failed to initialize Bitstamp WebSocket:', error);
                        }
                    }

                    if (!window.bybitWsManager) {
                        try {
                            window.bybitWsManager = new WebSocketManager(
                                'wss://stream.bybit.com/v5/public/linear',
                                'bybit',
                                { reconnectDelay: 2000 }
                            );
                        } catch (error) {
                            console.warn('Failed to initialize Bybit WebSocket:', error);
                        }
                    }
                }
            }

            // Set up basic event handlers
            this.setupEventHandlers();

            // Add missing CVD subscription stub
            this.addCVDStubs();
        },

        setupEventHandlers() {
            // Basic pair button functionality with smooth switching
            document.addEventListener('click', (event) => {
                const target = event.target;
                if (target.classList.contains('pair-button') && target.dataset.pair) {
                    this.switchToPair(target.dataset.pair);
                }
            });
        },

        switchToPair(newPair) {
            if (window.currentPair === newPair) return;

            // Show loading state
            const chartContainer = document.querySelector('.price-chart-container');
            if (chartContainer) {
                const loadingOverlay = chartContainer.querySelector('.loading-overlay');
                if (loadingOverlay) {
                    loadingOverlay.style.display = 'flex';
                    loadingOverlay.textContent = `Loading ${newPair} data...`;
                }

                // Add fade effect
                chartContainer.style.opacity = '0.7';
                setTimeout(() => {
                    chartContainer.style.opacity = '1';
                    if (loadingOverlay) {
                        loadingOverlay.style.display = 'none';
                    }
                }, 800);
            }

            // Update active states
            document.querySelectorAll('.pair-button').forEach(btn => {
                btn.classList.toggle('active', btn.dataset.pair === newPair);
            });

            // Update global state
            window.currentPair = newPair;
            window.currentActivePair = newPair;

            // Update container
            const container = document.querySelector('.chart-container');
            if (container) {
                container.dataset.pair = newPair;
            }

            // Call existing chart switching if available
            console.log(`[simpleLoader] switchToPair called with: ${newPair}`);
            console.log(`[simpleLoader] switchPairInternal available:`, !!window.switchPairInternal);
            console.log(`[simpleLoader] switchPair available:`, !!window.switchPair);
            
            if (window.switchPairInternal) {
                console.log(`[simpleLoader] Calling switchPairInternal for ${newPair}`);
                window.switchPairInternal(newPair);
            } else if (window.switchPair) {
                console.log(`[simpleLoader] Calling switchPair for ${newPair}`);
                window.switchPair(newPair);
            } else {
                console.warn(`[simpleLoader] No chart switching function available`);
            }

            // Dispatch event
            document.dispatchEvent(new CustomEvent('pairChanged', {
                detail: { pair: newPair }
            }));

            // Pair switched successfully
        },

        addCVDStubs() {
            // Add missing CVD functions to prevent errors
            window.PS = window.PS || {};

            // Add PerpCVD data store stubs
            if (!window.PS.getCurrentPerpCVD) {
                window.PS.getCurrentPerpCVD = function() {
                    return { time: Date.now() / 1000, value: 0 };
                };
            }

            if (!window.PS.setPerpCVD) {
                window.PS.setPerpCVD = function(data) {
                    // PerpCVD stub function
                };
            }

            // Add PerpImbalance data store stubs
            if (!window.PS.getCurrentPerpImbalance) {
                window.PS.getCurrentPerpImbalance = function() {
                    return { time: Date.now() / 1000, value: 0 };
                };
            }

            if (!window.PS.setPerpImbalance) {
                window.PS.setPerpImbalance = function(data) {
                    // PerpImbalance stub function
                };
            }

            // Add IndicatorChartUtils stub
            if (!window.IndicatorChartUtils) {
                window.IndicatorChartUtils = {
                    synchronizeCharts: function(components, priceChart, options) {
                        console.log('IndicatorChartUtils.synchronizeCharts - stub function');
                        return { unsubscribe: function() { console.log('IndicatorChartUtils unsubscribe - stub'); } };
                    },
                    cleanupIndicator: function(components, intervals, resetState) {
                        console.log('IndicatorChartUtils.cleanupIndicator - stub function');
                        if (intervals && Array.isArray(intervals)) {
                            intervals.forEach(interval => {
                                if (interval && typeof interval === 'number') {
                                    clearInterval(interval);
                                }
                            });
                        }
                    }
                };
            }
        },

        addGeneralStubs() {
            // Add BaseProfile stub if not loaded
            if (!window.BaseProfile) {
                window.BaseProfile = function() {
                    return {
                        padRange: function(min, max, percentage) {
                            const padding = (max - min) * percentage;
                            return { min: min - padding, max: max + padding };
                        }
                    };
                };
            }

            // Add CleanupManager stub if not loaded
            if (!window.CleanupManager) {
                let cleanupStubLogged = false;
                window.CleanupManager = {
                    registerCleanup: function(fn) {
                        // No logging or warnings; stub is silent
                    },
                    runAllCleanups: function() { /* console.log('CleanupManager.runAllCleanups - stub'); */ },
                    getCleanupCount: function() { return 0; }
                };
            }

            // Add chartSwitcher stub if not loaded
            if (!window.chartSwitcher) {
                window.chartSwitcher = {
                    switchTo: function(pair) { /* console.log('chartSwitcher.switchTo - stub', pair); */ },
                    getStats: function() { return {}; },
                    clearCache: function() { /* console.log('chartSwitcher.clearCache - stub'); */ }
                };
            }

            // Add performanceOptimizer stub if not loaded
            if (!window.performanceOptimizer) {
                window.performanceOptimizer = {
                    init: function() { /* no-op stub */ },
                    getMetrics: function() { return { longTaskCount: 0, cls: 0 }; },
                    cleanup: function() { /* no-op stub */ }
                };
            }

            // Add eventBus stub if not loaded
            if (!window.eventBus) {
                window.eventBus = {
                    events: {},
                    subscribe: function(event, callback) {
                        window.eventBus.events[event] = window.eventBus.events[event] || [];
                        window.eventBus.events[event].push(callback);
                        return function() {
                            window.eventBus.events[event] = window.eventBus.events[event].filter(cb => cb !== callback);
                        };
                    },
                    publish: function(event, data) {
                        if (window.eventBus.events[event]) {
                            window.eventBus.events[event].forEach(callback => {
                                try {
                                    callback(data);
                                } catch (err) {
                                    console.error(`Error in ${event}:`, err);
                                }
                            });
                        }
                    }
                };
            }

            // Add chartOrderbook stub if not loaded
            if (!window.chartOrderbook) {
                window.chartOrderbook = {
                    updateOrderBookLines: function(state) {
                        // console.log('chartOrderbook.updateOrderBookLines - stub');
                    },
                    clearOrderBookLines: function(state) {
                        // console.log('chartOrderbook.clearOrderBookLines - stub');
                    },
                    updateLastPrice: function(state, price) {
                        // console.log('chartOrderbook.updateLastPrice - stub');
                    }
                };
            }
        }
    };

    // Auto-start if not already loading
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            if (!document.body.classList.contains('dashboard-ready')) {
                window.simpleLoader = SimpleLoader;
                window.simpleLoader.init();
            }
        });
    } else {
        if (!document.body.classList.contains('dashboard-ready')) {
            window.simpleLoader = SimpleLoader;
            window.simpleLoader.init();
        }
    }

})();
