/**
 * DOM Cache
 * Caches and manages DOM element lookups for performance
 * Uses centralized logger and error handler for all logging and error reporting
 */

(function () {
  "use strict";

  const logger = window.logger
    ? window.logger.createLogger("DOMCache")
    : console;

  /**
   * Enhanced DOM Cache class for efficient element lookups with performance monitoring
   */
  class DOMCache {
    /**
     * Create a new DOM cache
     */
    constructor() {
      this.elements = new Map();
      this.selectors = new Map();
      this.mutationObserver = null;
      this.batchOperations = [];
      this.isProcessingBatch = false;

      // Performance monitoring
      this.stats = {
        hits: 0,
        misses: 0,
        selectorHits: 0,
        selectorMisses: 0,
        batchOperations: 0,
        averageQueryTime: 0,
        totalQueries: 0
      };

      this.performanceMetrics = {
        slowQueries: [],
        queryTimes: []
      };

      this.setupMutationObserver();

      // Cleanup on page unload
      window.addEventListener('beforeunload', () => this.destroy());
    }

    /**
     * Update performance metrics
     * @private
     */
    updatePerformanceMetrics(startTime) {
      const queryTime = performance.now() - startTime;
      this.stats.totalQueries++;
      this.performanceMetrics.queryTimes.push(queryTime);

      // Keep only last 1000 query times
      if (this.performanceMetrics.queryTimes.length > 1000) {
        this.performanceMetrics.queryTimes.shift();
      }

      // Track slow queries (>5ms)
      if (queryTime > 5) {
        this.performanceMetrics.slowQueries.push({
          time: queryTime,
          timestamp: Date.now()
        });

        // Keep only last 100 slow queries
        if (this.performanceMetrics.slowQueries.length > 100) {
          this.performanceMetrics.slowQueries.shift();
        }
      }

      // Update average
      this.stats.averageQueryTime = this.performanceMetrics.queryTimes.reduce((a, b) => a + b, 0) / this.performanceMetrics.queryTimes.length;
    }

    /**
     * Get an element by ID with performance monitoring
     * @param {string} id - Element ID
     * @returns {HTMLElement|null} - The element or null if not found
     */
    getElementById(id) {
      if (!id) return null;

      const startTime = performance.now();

      // Return from cache if exists
      if (this.elements.has(id)) {
        const element = this.elements.get(id);

        // Verify element is still in DOM
        if (element && document.contains(element)) {
          this.stats.hits++;
          this.updatePerformanceMetrics(startTime);
          return element;
        }

        // Element no longer in DOM, remove from cache
        this.elements.delete(id);
      }

      // Cache miss - query DOM
      this.stats.misses++;
      const element = document.getElementById(id);

      // Cache if found
      if (element) {
        this.elements.set(id, element);
      }

      this.updatePerformanceMetrics(startTime);
      return element;
    }

    /**
     * Batch DOM operations for better performance
     * @param {Function} operations - Function containing DOM operations
     * @returns {*} - Result of operations function
     */
    batch(operations) {
      if (this.isProcessingBatch) {
        // Already in batch, just execute
        return operations();
      }

      this.isProcessingBatch = true;
      this.stats.batchOperations++;

      try {
        const result = operations();

        // Process any queued operations
        if (this.batchOperations.length > 0) {
          const fragment = document.createDocumentFragment();
          this.batchOperations.forEach(op => {
            if (op.type === 'append' && op.parent && op.element) {
              fragment.appendChild(op.element);
            }
          });

          // Apply all at once
          if (fragment.children.length > 0) {
            document.body.appendChild(fragment);
          }

          this.batchOperations = [];
        }

        return result;
      } finally {
        this.isProcessingBatch = false;
      }
    }

    /**
     * Get elements by selector with performance monitoring
     * @param {string} selector - CSS selector
     * @param {boolean} [forceRefresh=false] - Whether to force a refresh
     * @returns {Array<HTMLElement>} - Array of elements
     */
    querySelectorAll(selector, forceRefresh = false) {
      if (!selector) return [];

      const startTime = performance.now();

      // Return from cache if exists and not forcing refresh
      if (!forceRefresh && this.selectors.has(selector)) {
        const elements = this.selectors.get(selector);

        // Verify all elements are still in DOM
        if (elements.every((el) => document.contains(el))) {
          this.stats.selectorHits++;
          this.updatePerformanceMetrics(startTime);
          return [...elements]; // Return copy to prevent mutation
        }

        // Some elements no longer in DOM, remove from cache
        this.selectors.delete(selector);
      }

      // Cache miss - query DOM
      this.stats.selectorMisses++;
      const elements = Array.from(document.querySelectorAll(selector));

      // Cache result
      this.selectors.set(selector, elements);

      this.updatePerformanceMetrics(startTime);
      return [...elements]; // Return copy to prevent mutation
    }

    /**
     * Get first element matching selector
     * @param {string} selector - CSS selector
     * @param {boolean} [forceRefresh=false] - Whether to force a refresh
     * @returns {HTMLElement|null} - The element or null if not found
     */
    querySelector(selector, forceRefresh = false) {
      if (!selector) return null;

      // Use cached selector results if available
      const elements = this.querySelectorAll(selector, forceRefresh);
      return elements.length > 0 ? elements[0] : null;
    }

    /**
     * Create element with attributes and properties
     * @param {string} tagName - HTML tag name
     * @param {Object} [options={}] - Element options
     * @param {Object} [options.attributes={}] - HTML attributes
     * @param {Object} [options.properties={}] - Element properties
     * @param {Object} [options.dataset={}] - Dataset attributes
     * @param {Object} [options.style={}] - Style properties
     * @param {Array|HTMLElement|string} [options.children] - Child elements
     * @param {Object} [options.events={}] - Event listeners
     * @returns {HTMLElement} - The created element
     */
    createElement(tagName, options = {}) {
      const element = document.createElement(tagName);

      // Set attributes
      if (options.attributes) {
        Object.entries(options.attributes).forEach(([key, value]) => {
          if (value !== null && value !== undefined) {
            element.setAttribute(key, value);
          }
        });
      }

      // Set properties
      if (options.properties) {
        Object.entries(options.properties).forEach(([key, value]) => {
          if (value !== null && value !== undefined) {
            element[key] = value;
          }
        });
      }

      // Set dataset
      if (options.dataset) {
        Object.entries(options.dataset).forEach(([key, value]) => {
          if (value !== null && value !== undefined) {
            element.dataset[key] = value;
          }
        });
      }

      // Set style
      if (options.style) {
        Object.entries(options.style).forEach(([key, value]) => {
          if (value !== null && value !== undefined) {
            element.style[key] = value;
          }
        });
      }

      // Add event listeners
      if (options.events) {
        Object.entries(options.events).forEach(([event, handler]) => {
          if (typeof handler === "function") {
            element.addEventListener(event, handler);
          }
        });
      }

      // Add children
      if (options.children) {
        if (Array.isArray(options.children)) {
          options.children.forEach((child) => {
            if (child) {
              if (typeof child === "string") {
                element.appendChild(document.createTextNode(child));
              } else if (child instanceof Node) {
                element.appendChild(child);
              }
            }
          });
        } else if (typeof options.children === "string") {
          element.textContent = options.children;
        } else if (options.children instanceof Node) {
          element.appendChild(options.children);
        }
      }

      return element;
    }

    /**
     * Set up mutation observer to invalidate cache when DOM changes
     * @private
     */
    setupMutationObserver() {
      // Skip if not supported
      if (!window.MutationObserver) return;

      this.mutationObserver = new MutationObserver((mutations) => {
        let shouldInvalidateSelectors = false;

        for (const mutation of mutations) {
          // For removed nodes
          if (mutation.removedNodes.length > 0) {
            for (const node of mutation.removedNodes) {
              if (node.id && this.elements.has(node.id)) {
                this.elements.delete(node.id);
              }

              // If structural change, invalidate selectors
              if (node.nodeType === Node.ELEMENT_NODE) {
                shouldInvalidateSelectors = true;
              }
            }
          }

          // For added nodes with IDs
          if (mutation.addedNodes.length > 0) {
            shouldInvalidateSelectors = true;
          }

          // For attribute changes on ID
          if (
            mutation.type === "attributes" &&
            mutation.attributeName === "id"
          ) {
            const target = mutation.target;
            const oldId = mutation.oldValue;
            const newId = target.id;

            if (oldId && this.elements.has(oldId)) {
              this.elements.delete(oldId);
              if (newId) {
                this.elements.set(newId, target);
              }
            }

            shouldInvalidateSelectors = true;
          }
        }

        // Clear selector cache if needed
        if (shouldInvalidateSelectors) {
          this.selectors.clear();
        }
      });

      // Start observing
      this.mutationObserver.observe(document.documentElement, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ["id"],
        attributeOldValue: true,
      });
    }

    /**
     * Get performance statistics
     * @returns {Object} - Performance statistics
     */
    getStats() {
      return {
        ...this.stats,
        cacheSize: this.elements.size,
        selectorCacheSize: this.selectors.size,
        hitRate: this.stats.totalQueries > 0 ? (this.stats.hits / this.stats.totalQueries * 100).toFixed(2) + '%' : '0%',
        selectorHitRate: (this.stats.selectorHits + this.stats.selectorMisses) > 0 ?
          (this.stats.selectorHits / (this.stats.selectorHits + this.stats.selectorMisses) * 100).toFixed(2) + '%' : '0%',
        slowQueriesCount: this.performanceMetrics.slowQueries.length
      };
    }

    /**
     * Clear the cache
     */
    clear() {
      this.elements.clear();
      this.selectors.clear();

      // Reset stats
      this.stats.hits = 0;
      this.stats.misses = 0;
      this.stats.selectorHits = 0;
      this.stats.selectorMisses = 0;
      this.stats.totalQueries = 0;
      this.performanceMetrics.queryTimes = [];
      this.performanceMetrics.slowQueries = [];
    }

    /**
     * Clean up resources
     */
    destroy() {
      if (this.mutationObserver) {
        this.mutationObserver.disconnect();
        this.mutationObserver = null;
      }

      this.clear();
      this.batchOperations = [];
    }
  }

  // Create global instance
  window.domCache = new DOMCache();

  // Add shorthand methods to window
  window.$id = (id) => window.domCache.getElementById(id);
  window.$query = (selector, forceRefresh) =>
    window.domCache.querySelector(selector, forceRefresh);
  window.$queryAll = (selector, forceRefresh) =>
    window.domCache.querySelectorAll(selector, forceRefresh);
  window.$create = (tagName, options) =>
    window.domCache.createElement(tagName, options);
})();
