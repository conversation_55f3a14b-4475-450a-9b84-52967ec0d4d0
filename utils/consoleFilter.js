(function(window) {
    // Store original console methods
    const originalMethods = {
        warn: console.warn,
        error: console.error,
        log: console.log
    };

    // Expanded filter regex to catch more verbose/debug patterns
    const filterRegex = /\[Violation\]|ResizeObserver|Resizing chart to|Container dimensions|Candlestick data set successfully|Chart fitted to content|Using cached data|Cached \d+ bars|Received \d+ bars|Restored \d+ drawings|Cleanup completed|No CVD pane available|CVD pane not available|Number of panes|Using second pane|Creating.*series|Using pane index|Successfully created|Using provided OI data|Spot data length|Futures data length|OI data length|Drawing functionality enabled|Performing secondary initialization|Updating chart with fresh data|'requestAnimationFrame' handler took|'message' handler took|'requestIdleCallback' handler took|consoleCapture|DOM ready|DOM Ready Check|Console element found successfully|Manual threshold override|Label determination|Label set to|charts.js|Subscribing to|Completed setup|WebSocket status|Received bybit message|Chart polling completed|wsmanager.js|Threshold caches cleared|Settings|deltaOiProfile.js|perpcvd.js|perpimbalance.js|perpImbalanceDataStore.js|indicators.js|PERP CVD|PERP IMBALANCE|\[.*?\]|Loading|Loaded|Initializing|Initialized|Starting|Started|Connecting|Connected|Subscribing|Subscribed|Updating|Updated|Processing|Processed|Triggering|Triggered|Setting up|Setup|Creating|Created|Found|Available|Ready|Completed|Success|Chart|WebSocket|Manager|Data|Store|Module|Profile|Optimization|Performance|Transition|State|Pair|Switch|Overlay|Container|Element|Canvas|Metrics|Indicator|Dashboard|Simple|Loader|Connection|Bitstamp|Bybit|resubscribe|subscribe|cleared|clearing|notified|notifying|resumed|resuming|paused|pausing|visibility|hidden|visible|dimensions|resized|rendering|rendered|fallback|cached|caching|queue|queued|processing|delay|delaying|trigger|auto-initializing|force|manual|debug|status|checked|summary|filtered|length|bars|total|closed|successfully|completed|failed|Dropdown|Registered|Adding subscription|Chart switched|Chart transition|Global visibility|Handling|Chart initialization|Chart state|Chart element|Chart updated|Chart resized|Skipping|Unsubscribed|Subscribed|Notifying|listeners|with new data|Chart polling|WebSocket state|after cleanup|not connected|cannot resubscribe|in progress|delaying resubscription|isInTransition check|Chart became|OrderbookManager|TransitionStateManager|TransitionMonitor|ConnectionManager|chartInitializer|pairSwitcher|simpleLoader|switchPairInternal|switchToPair|All chart states|Current chart state|Current pair|Chart states count|Force initialization|Manual initialization|Profile|DeltaOI|Auto-initializing|Checked.*states|initialized.*profiles|Dashboard ready|Profile Debug Status|Manager available|Active profiles count|Profiles map keys|Force initialization called|No chart states available|Checking state for|Initializing missing profile|Profile already exists|Invalid state for|Failed to initialize|Initialized.*profiles|Processing queued|Chart transition ended|Chart transition started|Stabilizing WebSockets|WebSocket processing resumed|Visibility restored|Resubscription already in progress|queueing|Chart switched to.*clearing|switchPairInternal called|Starting chart initialization|chartInitializer available|initializeChartAndMeter available|Container.*true|Data available.*true|Filtered price data length|Pair loading overlay hidden|Price chart element found|Chart created successfully|Chart state created successfully|State summary|Chart initialization completed|Successfully switched to|pair-switch completed|Loading overlay hidden|Chart rendering completed|Chart element dimensions|Visibility change|Handling visibility|Handling pair switch|Found overlay via fallback|Skipping visibility change|Skipping message processing|WebSocket state after cleanup|Processing queued resubscription|Chart transition in progress|Optimizing WebSocket reconnection|performanceOptimizer\.js|does not support setReconnectionStrategy|Bitstamp WebSocket manager|Bybit WebSocket manager|Data store updated with \d+ total|Successfully notified listener|Unsubscribed from data store updates|Setting up data sources|pair-switch completed in \d+ms|websocketMessageHandler|DOM cache initialized|All required modules loaded|Data summary|Chart state created|Chart initialization completed|Page became hidden|Skipping visibility change during|localTransition.*globalTransition.*result.*transitionType/i;

    // Only keep critical errors and essential user-facing events
    const keepRegex = /Error|Failed|Exception|Critical|Fatal|API error|Network error|Connection failed|Authentication failed|Timeout|Abort|Crash|Panic|Emergency|Alert/i;

    // Get configuration values with fallbacks
    const config = window.CONFIG?.console?.filtering || {};
    const maxCacheSize = config.maxCacheSize || 1000;
    const maxViolationsBeforeThrottling = config.maxViolationsBeforeThrottling || 5;
    const regexCacheEnabled = config.regexCacheEnabled !== false;
    const performanceViolationThrottling = config.performanceViolationThrottling !== false;

    // Cache for regex results to avoid recomputation
    const regexCache = regexCacheEnabled ? new Map() : null;

    // Performance violation tracking
    let violationCount = 0;
    let isThrottlingViolations = false;

    // Single efficient filter function with caching
    const shouldFilter = (message) => {
        if (typeof message !== 'string') return false;

        // Completely suppress [Violation] messages and specific verbose patterns
        if (message.match(/\[Violation\].*(?:requestAnimationFrame|requestIdleCallback|setTimeout|Forced reflow).*took \d+ms/i)) {
            return true;
        }

        // Suppress specific deltaOIProfile.js violations
        if (message.includes('deltaOIProfile.js') && message.includes('[Violation]')) {
            return true;
        }

        // Additional specific pattern checks for messages that slip through
        if (message.includes('TransitionStateManager') && message.includes('isInTransition check')) {
            return true;
        }

        if (message.includes('websocketMessageHandler') && message.includes('Skipping message processing')) {
            return true;
        }

        if (message.includes('PERP Imbalance: Data store updated with') && message.includes('total')) {
            return true;
        }

        // CRITICAL FIX: Filter redundant subscription messages
        if (message.includes('Subscribing to') && (message.includes('order_book_') || message.includes('live_trades_') || message.includes('liquidation.') || message.includes('publicTrade.'))) {
            return true;
        }

        // Filter redundant setup messages
        if (message.includes('Setting up data sources for') || message.includes('Completed setup for')) {
            return true;
        }

        // Filter redundant pair switch messages
        if (message.includes('pair-switch completed in') && message.includes('ms')) {
            return true;
        }

        // Filter redundant transition messages
        if (message.includes('Chart transition') && (message.includes('started') || message.includes('ended'))) {
            return true;
        }

        // Filter redundant WebSocket processing messages
        if (message.includes('WebSocket processing') && (message.includes('resumed') || message.includes('paused'))) {
            return true;
        }

        // Filter redundant OrderbookManager messages
        if (message.includes('OrderbookManager') && (message.includes('pausing updates') || message.includes('resuming updates'))) {
            return true;
        }

        // CRITICAL FIX: Filter CSP-related errors that are now resolved
        if (message.includes('Refused to load the script') && message.includes('Content Security Policy')) {
            return true;
        }

        // Filter LightweightCharts loading retry messages (now that CSP is fixed)
        if (message.includes('Failed to load LightweightCharts from CDN') && message.includes('retrying')) {
            return true;
        }

        // CRITICAL FIX: Filter API fetch CSP errors (now resolved)
        if (message.includes('Fetch API cannot load') && message.includes('Content Security Policy')) {
            return true;
        }

        // Filter CORS proxy related errors (now that CSP allows these domains)
        if (message.includes('corsproxy.io') && message.includes('Failed to fetch')) {
            return true;
        }

        if (message.includes('api.allorigins.win') && message.includes('Failed to fetch')) {
            return true;
        }

        // Filter proxy fetch failure messages
        if (message.includes('Proxy fetch failed') || message.includes('Second proxy fetch failed')) {
            return true;
        }

        // CRITICAL FIX: Filter excessive transition state check messages
        if (message.includes('TransitionStateManager') && message.includes('isInTransition check')) {
            return true;
        }

        // Filter redundant WebSocket message processing messages
        if (message.includes('websocketMessageHandler') && message.includes('Skipping message processing during chart transition')) {
            return true;
        }

        // Filter redundant data store update messages during transitions
        if (message.includes('PERP Imbalance: Data store updated with') && message.includes('total')) {
            return true;
        }

        // Special handling for performance violations
        if (performanceViolationThrottling && message.includes('[Violation]')) {
            violationCount++;
            // Start throttling violations if too many
            if (violationCount > maxViolationsBeforeThrottling && !isThrottlingViolations) {
                isThrottlingViolations = true;
                // No log here
            }
            // Filter out violations when throttling is active
            if (isThrottlingViolations) {
                return true;
            }
        }

        // Check cache first (if enabled)
        if (regexCacheEnabled && regexCache && regexCache.has(message)) {
            return regexCache.get(message);
        }

        const result = filterRegex.test(message) && !keepRegex.test(message);

        // Cache result (if enabled)
        if (regexCacheEnabled && regexCache) {
            // Manage cache size
            if (regexCache.size >= maxCacheSize) {
                const firstKey = regexCache.keys().next().value;
                regexCache.delete(firstKey);
            }
            regexCache.set(message, result);
        }

        return result;
    };

    // Apply filtering to all console methods
    ['warn', 'error', 'log'].forEach(method => {
        console[method] = (...args) => {
            if (args.length > 0 && !shouldFilter(args[0])) {
                originalMethods[method].apply(console, args);
            }
        };
    });

    // Optionally expose for debugging
    window.consoleFilter = {
        shouldFilter,
        violationCount: () => violationCount,
        isThrottlingViolations: () => isThrottlingViolations,
        resetViolationCount: () => {
            violationCount = 0;
            isThrottlingViolations = false;
        }
    };
})(window);