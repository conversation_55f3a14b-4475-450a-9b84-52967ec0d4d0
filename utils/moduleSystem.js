/**
 * Module System for PeckerSocket 7.0
 * Provides dependency injection and reduces global variable pollution
 */

(function() {
  'use strict';

  const logger = window.logger ? window.logger.createLogger('ModuleSystem') : console;

  /**
   * Module Registry for managing application modules
   */
  class ModuleRegistry {
    constructor() {
      this.modules = new Map();
      this.dependencies = new Map();
      this.instances = new Map();
      this.loading = new Set();
      this.stats = {
        registered: 0,
        instantiated: 0,
        failed: 0
      };
    }

    /**
     * Register a module with its dependencies
     * @param {string} name - Module name
     * @param {Function} factory - Module factory function
     * @param {string[]} dependencies - Array of dependency names
     * @param {Object} options - Module options
     */
    register(name, factory, dependencies = [], options = {}) {
      if (this.modules.has(name)) {
        logger.warn(`Module ${name} is already registered`);
        return false;
      }

      this.modules.set(name, {
        name,
        factory,
        dependencies,
        options: {
          singleton: true,
          lazy: false,
          ...options
        },
        registered: Date.now()
      });

      this.dependencies.set(name, dependencies);
      this.stats.registered++;

      logger.debug(`Registered module: ${name} with dependencies: [${dependencies.join(', ')}]`);
      return true;
    }

    /**
     * Get a module instance, creating it if necessary
     * @param {string} name - Module name
     * @returns {*} Module instance
     */
    async get(name) {
      // Return existing instance if singleton
      if (this.instances.has(name)) {
        const moduleInfo = this.modules.get(name);
        if (moduleInfo?.options.singleton) {
          return this.instances.get(name);
        }
      }

      // Check for circular dependencies
      if (this.loading.has(name)) {
        throw new Error(`Circular dependency detected for module: ${name}`);
      }

      return this.instantiate(name);
    }

    /**
     * Instantiate a module with its dependencies
     * @private
     */
    async instantiate(name) {
      const moduleInfo = this.modules.get(name);
      if (!moduleInfo) {
        throw new Error(`Module not found: ${name}`);
      }

      this.loading.add(name);

      try {
        // Resolve dependencies
        const dependencies = {};
        for (const depName of moduleInfo.dependencies) {
          dependencies[depName] = await this.get(depName);
        }

        // Create instance
        const instance = await moduleInfo.factory(dependencies);
        
        // Store instance if singleton
        if (moduleInfo.options.singleton) {
          this.instances.set(name, instance);
        }

        this.stats.instantiated++;
        logger.debug(`Instantiated module: ${name}`);
        
        return instance;
      } catch (error) {
        this.stats.failed++;
        if (window.errorHandler) {
          window.errorHandler.handleError(error, {
            source: 'moduleSystem',
            component: 'registry',
            operation: 'instantiate',
            context: { moduleName: name }
          });
        }
        throw error;
      } finally {
        this.loading.delete(name);
      }
    }

    /**
     * Check if a module is registered
     * @param {string} name - Module name
     * @returns {boolean} Whether module is registered
     */
    has(name) {
      return this.modules.has(name);
    }

    /**
     * Unregister a module
     * @param {string} name - Module name
     */
    unregister(name) {
      if (this.modules.has(name)) {
        this.modules.delete(name);
        this.dependencies.delete(name);
        this.instances.delete(name);
        logger.debug(`Unregistered module: ${name}`);
        return true;
      }
      return false;
    }

    /**
     * Get module statistics
     * @returns {Object} Statistics object
     */
    getStats() {
      return {
        ...this.stats,
        totalModules: this.modules.size,
        activeInstances: this.instances.size,
        loadingModules: this.loading.size
      };
    }

    /**
     * Get dependency graph
     * @returns {Object} Dependency graph
     */
    getDependencyGraph() {
      const graph = {};
      for (const [name, deps] of this.dependencies.entries()) {
        graph[name] = deps;
      }
      return graph;
    }

    /**
     * Validate dependency graph for circular dependencies
     * @returns {boolean} Whether graph is valid
     */
    validateDependencies() {
      const visited = new Set();
      const visiting = new Set();

      const visit = (name) => {
        if (visiting.has(name)) {
          throw new Error(`Circular dependency detected: ${name}`);
        }
        if (visited.has(name)) {
          return;
        }

        visiting.add(name);
        const deps = this.dependencies.get(name) || [];
        for (const dep of deps) {
          visit(dep);
        }
        visiting.delete(name);
        visited.add(name);
      };

      try {
        for (const name of this.modules.keys()) {
          visit(name);
        }
        return true;
      } catch (error) {
        logger.error('Dependency validation failed:', error);
        return false;
      }
    }

    /**
     * Clear all modules and instances
     */
    clear() {
      this.modules.clear();
      this.dependencies.clear();
      this.instances.clear();
      this.loading.clear();
      this.stats = { registered: 0, instantiated: 0, failed: 0 };
    }
  }

  /**
   * Service Locator for accessing services without global variables
   */
  class ServiceLocator {
    constructor() {
      this.services = new Map();
      this.factories = new Map();
    }

    /**
     * Register a service
     * @param {string} name - Service name
     * @param {*} service - Service instance or factory
     * @param {boolean} isFactory - Whether service is a factory function
     */
    register(name, service, isFactory = false) {
      if (isFactory) {
        this.factories.set(name, service);
      } else {
        this.services.set(name, service);
      }
    }

    /**
     * Get a service
     * @param {string} name - Service name
     * @returns {*} Service instance
     */
    get(name) {
      // Return existing service
      if (this.services.has(name)) {
        return this.services.get(name);
      }

      // Create from factory
      if (this.factories.has(name)) {
        const factory = this.factories.get(name);
        const service = factory();
        this.services.set(name, service);
        return service;
      }

      throw new Error(`Service not found: ${name}`);
    }

    /**
     * Check if service exists
     * @param {string} name - Service name
     * @returns {boolean} Whether service exists
     */
    has(name) {
      return this.services.has(name) || this.factories.has(name);
    }

    /**
     * Remove a service
     * @param {string} name - Service name
     */
    remove(name) {
      this.services.delete(name);
      this.factories.delete(name);
    }

    /**
     * Clear all services
     */
    clear() {
      this.services.clear();
      this.factories.clear();
    }

    /**
     * Get all service names
     * @returns {string[]} Array of service names
     */
    getServiceNames() {
      return [
        ...Array.from(this.services.keys()),
        ...Array.from(this.factories.keys())
      ];
    }
  }

  /**
   * Event Bus for decoupled communication between modules
   */
  class EventBus {
    constructor() {
      this.listeners = new Map();
      this.stats = {
        events: 0,
        listeners: 0,
        emitted: 0
      };
    }

    /**
     * Subscribe to an event
     * @param {string} event - Event name
     * @param {Function} listener - Event listener
     * @returns {Function} Unsubscribe function
     */
    on(event, listener) {
      if (!this.listeners.has(event)) {
        this.listeners.set(event, new Set());
        this.stats.events++;
      }

      this.listeners.get(event).add(listener);
      this.stats.listeners++;

      // Return unsubscribe function
      return () => {
        const eventListeners = this.listeners.get(event);
        if (eventListeners) {
          eventListeners.delete(listener);
          this.stats.listeners--;
          
          if (eventListeners.size === 0) {
            this.listeners.delete(event);
            this.stats.events--;
          }
        }
      };
    }

    /**
     * Subscribe to an event once
     * @param {string} event - Event name
     * @param {Function} listener - Event listener
     * @returns {Function} Unsubscribe function
     */
    once(event, listener) {
      const unsubscribe = this.on(event, (...args) => {
        unsubscribe();
        listener(...args);
      });
      return unsubscribe;
    }

    /**
     * Emit an event
     * @param {string} event - Event name
     * @param {...*} args - Event arguments
     */
    emit(event, ...args) {
      const eventListeners = this.listeners.get(event);
      if (eventListeners) {
        for (const listener of eventListeners) {
          try {
            listener(...args);
          } catch (error) {
            if (window.errorHandler) {
              window.errorHandler.handleError(error, {
                source: 'moduleSystem',
                component: 'eventBus',
                operation: 'emit',
                context: { event, args }
              });
            } else {
              logger.error(`Error in event listener for ${event}:`, error);
            }
          }
        }
        this.stats.emitted++;
      }
    }

    /**
     * Remove all listeners for an event
     * @param {string} event - Event name
     */
    off(event) {
      const eventListeners = this.listeners.get(event);
      if (eventListeners) {
        this.stats.listeners -= eventListeners.size;
        this.listeners.delete(event);
        this.stats.events--;
      }
    }

    /**
     * Get event bus statistics
     * @returns {Object} Statistics object
     */
    getStats() {
      return { ...this.stats };
    }

    /**
     * Clear all listeners
     */
    clear() {
      this.listeners.clear();
      this.stats = { events: 0, listeners: 0, emitted: 0 };
    }
  }

  // Create global instances
  const moduleRegistry = new ModuleRegistry();
  const serviceLocator = new ServiceLocator();
  const eventBus = new EventBus();

  // Cleanup on page unload
  window.addEventListener('beforeunload', () => {
    moduleRegistry.clear();
    serviceLocator.clear();
    eventBus.clear();
  });

  /**
   * State Manager for centralized state management
   */
  class StateManager {
    constructor() {
      this.state = new Map();
      this.subscribers = new Map();
      this.history = [];
      this.maxHistorySize = 100;
    }

    /**
     * Set state value
     * @param {string} key - State key
     * @param {*} value - State value
     * @param {boolean} notify - Whether to notify subscribers
     */
    set(key, value, notify = true) {
      const oldValue = this.state.get(key);
      this.state.set(key, value);

      // Add to history
      this.history.push({
        key,
        oldValue,
        newValue: value,
        timestamp: Date.now()
      });

      // Limit history size
      if (this.history.length > this.maxHistorySize) {
        this.history.shift();
      }

      // Notify subscribers
      if (notify && this.subscribers.has(key)) {
        const keySubscribers = this.subscribers.get(key);
        for (const subscriber of keySubscribers) {
          try {
            subscriber(value, oldValue, key);
          } catch (error) {
            logger.error(`Error in state subscriber for ${key}:`, error);
          }
        }
      }
    }

    /**
     * Get state value
     * @param {string} key - State key
     * @param {*} defaultValue - Default value if key doesn't exist
     * @returns {*} State value
     */
    get(key, defaultValue = undefined) {
      return this.state.has(key) ? this.state.get(key) : defaultValue;
    }

    /**
     * Subscribe to state changes
     * @param {string} key - State key
     * @param {Function} callback - Callback function
     * @returns {Function} Unsubscribe function
     */
    subscribe(key, callback) {
      if (!this.subscribers.has(key)) {
        this.subscribers.set(key, new Set());
      }

      this.subscribers.get(key).add(callback);

      // Return unsubscribe function
      return () => {
        const keySubscribers = this.subscribers.get(key);
        if (keySubscribers) {
          keySubscribers.delete(callback);
          if (keySubscribers.size === 0) {
            this.subscribers.delete(key);
          }
        }
      };
    }

    /**
     * Check if state key exists
     * @param {string} key - State key
     * @returns {boolean} Whether key exists
     */
    has(key) {
      return this.state.has(key);
    }

    /**
     * Delete state key
     * @param {string} key - State key
     */
    delete(key) {
      const oldValue = this.state.get(key);
      this.state.delete(key);

      // Notify subscribers of deletion
      if (this.subscribers.has(key)) {
        const keySubscribers = this.subscribers.get(key);
        for (const subscriber of keySubscribers) {
          try {
            subscriber(undefined, oldValue, key);
          } catch (error) {
            logger.error(`Error in state subscriber for ${key}:`, error);
          }
        }
      }
    }

    /**
     * Get all state keys
     * @returns {string[]} Array of state keys
     */
    keys() {
      return Array.from(this.state.keys());
    }

    /**
     * Get state history
     * @param {string} key - Optional key to filter history
     * @returns {Array} State history
     */
    getHistory(key = null) {
      if (key) {
        return this.history.filter(entry => entry.key === key);
      }
      return [...this.history];
    }

    /**
     * Clear all state
     */
    clear() {
      this.state.clear();
      this.subscribers.clear();
      this.history = [];
    }
  }

  // Create state manager instance
  const stateManager = new StateManager();

  // Export to global scope (minimal global footprint)
  window.ModuleSystem = {
    registry: moduleRegistry,
    services: serviceLocator,
    events: eventBus,
    state: stateManager,

    // Convenience methods
    register: (name, factory, deps, options) => moduleRegistry.register(name, factory, deps, options),
    get: (name) => moduleRegistry.get(name),
    service: (name, service, isFactory) => serviceLocator.register(name, service, isFactory),
    getService: (name) => serviceLocator.get(name),
    on: (event, listener) => eventBus.on(event, listener),
    emit: (event, ...args) => eventBus.emit(event, ...args),
    setState: (key, value, notify) => stateManager.set(key, value, notify),
    getState: (key, defaultValue) => stateManager.get(key, defaultValue),
    subscribeState: (key, callback) => stateManager.subscribe(key, callback)
  };

})();
