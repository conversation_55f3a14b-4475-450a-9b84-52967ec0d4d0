/**
 * Performance Optimizer
 * Utilities to reduce performance violations and optimize expensive operations
 */

(function() {
  'use strict';

  const logger = window.logger ? window.logger.createLogger('PerformanceOptimizer') : console;

  /**
   * Task Scheduler for breaking up long-running tasks
   */
  class TaskScheduler {
    constructor() {
      this.tasks = [];
      this.isRunning = false;
      this.maxExecutionTime = 4; // 4ms to stay under 5ms violation threshold
    }

    /**
     * Schedule a task to run in chunks
     * @param {Function} task - Task function
     * @param {Object} options - Task options
     * @returns {Promise} Promise that resolves when task completes
     */
    schedule(task, options = {}) {
      const {
        priority = 0,
        maxChunkTime = this.maxExecutionTime,
        onProgress = null
      } = options;

      return new Promise((resolve, reject) => {
        this.tasks.push({
          task,
          priority,
          maxChunkTime,
          onProgress,
          resolve,
          reject,
          startTime: performance.now()
        });

        this.tasks.sort((a, b) => b.priority - a.priority);
        
        if (!this.isRunning) {
          this.processNext();
        }
      });
    }

    /**
     * Process next task in queue
     * @private
     */
    async processNext() {
      if (this.tasks.length === 0) {
        this.isRunning = false;
        return;
      }

      this.isRunning = true;
      const taskInfo = this.tasks.shift();
      const { task, maxChunkTime, onProgress, resolve, reject } = taskInfo;

      try {
        const startTime = performance.now();
        let result;

        // Use requestIdleCallback if available
        if (window.requestIdleCallback) {
          result = await new Promise((taskResolve, taskReject) => {
            requestIdleCallback((deadline) => {
              try {
                const timeRemaining = Math.min(deadline.timeRemaining(), maxChunkTime);
                const taskResult = task(timeRemaining);
                taskResolve(taskResult);
              } catch (error) {
                taskReject(error);
              }
            }, { timeout: 100 });
          });
        } else {
          // Fallback to regular execution with time limit
          result = task(maxChunkTime);
        }

        const executionTime = performance.now() - startTime;
        
        if (onProgress) {
          onProgress({ executionTime, completed: true });
        }

        resolve(result);
      } catch (error) {
        reject(error);
      }

      // Schedule next task
      if (this.tasks.length > 0) {
        setTimeout(() => this.processNext(), 0);
      } else {
        this.isRunning = false;
      }
    }

    /**
     * Clear all pending tasks
     */
    clear() {
      this.tasks = [];
      this.isRunning = false;
    }

    /**
     * Get queue statistics
     * @returns {Object} Queue statistics
     */
    getStats() {
      return {
        pendingTasks: this.tasks.length,
        isRunning: this.isRunning,
        maxExecutionTime: this.maxExecutionTime
      };
    }
  }

  /**
   * Batch processor for array operations
   */
  class BatchProcessor {
    constructor(batchSize = 100) {
      this.batchSize = batchSize;
    }

    /**
     * Process array in batches to avoid blocking
     * @param {Array} array - Array to process
     * @param {Function} processor - Function to process each item
     * @param {Object} options - Processing options
     * @returns {Promise<Array>} Promise that resolves with results
     */
    async process(array, processor, options = {}) {
      const {
        batchSize = this.batchSize,
        onProgress = null,
        maxExecutionTime = 4
      } = options;

      const results = [];
      const totalItems = array.length;
      let processedItems = 0;

      for (let i = 0; i < array.length; i += batchSize) {
        const batch = array.slice(i, i + batchSize);
        const startTime = performance.now();

        // Process batch
        const batchResults = [];
        for (const item of batch) {
          // Check if we're approaching time limit
          if (performance.now() - startTime > maxExecutionTime) {
            // Yield control and continue in next frame
            await new Promise(resolve => setTimeout(resolve, 0));
            break;
          }

          batchResults.push(processor(item, i + batchResults.length));
        }

        results.push(...batchResults);
        processedItems += batchResults.length;

        // Report progress
        if (onProgress) {
          onProgress({
            processed: processedItems,
            total: totalItems,
            percentage: (processedItems / totalItems * 100).toFixed(1)
          });
        }

        // Yield control between batches
        if (i + batchSize < array.length) {
          await new Promise(resolve => setTimeout(resolve, 0));
        }
      }

      return results;
    }
  }

  /**
   * Performance monitor for detecting violations
   */
  class PerformanceMonitor {
    constructor() {
      this.violations = [];
      this.maxViolations = 100;
      this.thresholds = {
        longTask: 50, // ms
        animationFrame: 16, // ms (60fps)
        setTimeout: 4 // ms
      };
    }

    /**
     * Monitor a function for performance violations
     * @param {Function} fn - Function to monitor
     * @param {string} name - Function name for reporting
     * @param {string} type - Type of operation
     * @returns {Function} Monitored function
     */
    monitor(fn, name, type = 'general') {
      return (...args) => {
        const startTime = performance.now();
        
        try {
          const result = fn.apply(this, args);
          
          // Handle async functions
          if (result && typeof result.then === 'function') {
            return result.finally(() => {
              this.recordExecution(name, type, startTime);
            });
          } else {
            this.recordExecution(name, type, startTime);
            return result;
          }
        } catch (error) {
          this.recordExecution(name, type, startTime, error);
          throw error;
        }
      };
    }

    /**
     * Record function execution time
     * @private
     */
    recordExecution(name, type, startTime, error = null) {
      const executionTime = performance.now() - startTime;
      const threshold = this.thresholds[type] || this.thresholds.general;

      if (executionTime > threshold) {
        this.violations.push({
          name,
          type,
          executionTime,
          threshold,
          timestamp: Date.now(),
          error: error ? error.message : null
        });

        // Keep only recent violations
        if (this.violations.length > this.maxViolations) {
          this.violations.shift();
        }

        // Log warning for significant violations
        if (executionTime > threshold * 2) {
          logger.warn(`Performance violation: ${name} took ${executionTime.toFixed(2)}ms (threshold: ${threshold}ms)`);
        }
      }
    }

    /**
     * Get violation statistics
     * @returns {Object} Violation statistics
     */
    getStats() {
      const recentViolations = this.violations.filter(v => 
        Date.now() - v.timestamp < 60000 // Last minute
      );

      return {
        totalViolations: this.violations.length,
        recentViolations: recentViolations.length,
        averageExecutionTime: recentViolations.length > 0 ?
          recentViolations.reduce((sum, v) => sum + v.executionTime, 0) / recentViolations.length : 0,
        worstViolation: this.violations.length > 0 ?
          this.violations.reduce((worst, current) => 
            current.executionTime > worst.executionTime ? current : worst
          ) : null
      };
    }

    /**
     * Clear violation history
     */
    clear() {
      this.violations = [];
    }
  }

  /**
   * Optimized animation frame scheduler
   */
  class AnimationScheduler {
    constructor() {
      this.callbacks = [];
      this.isRunning = false;
      this.frameId = null;
    }

    /**
     * Schedule callback for next animation frame
     * @param {Function} callback - Callback function
     * @param {number} priority - Priority (higher = earlier execution)
     * @returns {Function} Cancel function
     */
    schedule(callback, priority = 0) {
      const callbackInfo = { callback, priority, id: Date.now() + Math.random() };
      
      this.callbacks.push(callbackInfo);
      this.callbacks.sort((a, b) => b.priority - a.priority);

      if (!this.isRunning) {
        this.start();
      }

      // Return cancel function
      return () => {
        const index = this.callbacks.findIndex(cb => cb.id === callbackInfo.id);
        if (index !== -1) {
          this.callbacks.splice(index, 1);
        }
      };
    }

    /**
     * Start animation loop
     * @private
     */
    start() {
      if (this.isRunning) return;
      
      this.isRunning = true;
      
      const frame = (timestamp) => {
        const frameStart = performance.now();
        const maxFrameTime = 14; // Leave 2ms buffer for 60fps

        // Execute callbacks within time budget
        while (this.callbacks.length > 0 && (performance.now() - frameStart) < maxFrameTime) {
          const { callback } = this.callbacks.shift();
          
          try {
            callback(timestamp);
          } catch (error) {
            logger.error('Error in animation callback:', error);
          }
        }

        // Continue if there are more callbacks
        if (this.callbacks.length > 0) {
          this.frameId = requestAnimationFrame(frame);
        } else {
          this.isRunning = false;
          this.frameId = null;
        }
      };

      this.frameId = requestAnimationFrame(frame);
    }

    /**
     * Stop animation loop
     */
    stop() {
      if (this.frameId) {
        cancelAnimationFrame(this.frameId);
        this.frameId = null;
      }
      this.isRunning = false;
    }

    /**
     * Clear all callbacks
     */
    clear() {
      this.callbacks = [];
      this.stop();
    }
  }

  // Create global instances
  const taskScheduler = new TaskScheduler();
  const batchProcessor = new BatchProcessor();
  const performanceMonitor = new PerformanceMonitor();
  const animationScheduler = new AnimationScheduler();

  // Export to global scope
  window.PerformanceOptimizer = {
    TaskScheduler,
    BatchProcessor,
    PerformanceMonitor,
    AnimationScheduler,
    
    // Global instances
    taskScheduler,
    batchProcessor,
    performanceMonitor,
    animationScheduler,
    
    // Convenience methods
    scheduleTask: (task, options) => taskScheduler.schedule(task, options),
    processBatch: (array, processor, options) => batchProcessor.process(array, processor, options),
    monitor: (fn, name, type) => performanceMonitor.monitor(fn, name, type),
    scheduleAnimation: (callback, priority) => animationScheduler.schedule(callback, priority)
  };

})();
