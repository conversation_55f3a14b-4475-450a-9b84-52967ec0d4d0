/**
 * Enhanced Event Management System
 * Provides automatic cleanup using AbortController and prevents memory leaks
 */

(function() {
  'use strict';

  const logger = window.logger ? window.logger.createLogger('EventManager') : console;

  /**
   * Event Manager class for handling event listeners with automatic cleanup
   */
  class EventManager {
    constructor(name = 'EventManager') {
      this.name = name;
      this.abortController = new AbortController();
      this.listeners = new Map(); // Track listeners for debugging
      this.stats = {
        added: 0,
        removed: 0,
        aborted: 0
      };
    }

    /**
     * Add event listener with automatic cleanup
     * @param {EventTarget} target - Event target
     * @param {string} type - Event type
     * @param {Function} listener - Event listener
     * @param {Object} options - Event options
     * @returns {Function} - Manual cleanup function
     */
    addEventListener(target, type, listener, options = {}) {
      if (!target || !type || typeof listener !== 'function') {
        throw new Error('Invalid parameters for addEventListener');
      }

      // Merge options with signal
      const eventOptions = {
        ...options,
        signal: this.abortController.signal
      };

      // Add listener
      target.addEventListener(type, listener, eventOptions);
      
      // Track for debugging
      const listenerId = `${target.constructor.name}-${type}-${Date.now()}`;
      this.listeners.set(listenerId, {
        target,
        type,
        listener,
        options: eventOptions,
        timestamp: Date.now()
      });

      this.stats.added++;

      // Return manual cleanup function
      return () => {
        target.removeEventListener(type, listener, eventOptions);
        this.listeners.delete(listenerId);
        this.stats.removed++;
      };
    }

    /**
     * Add multiple event listeners at once
     * @param {Array} listeners - Array of listener configurations
     * @returns {Array} - Array of cleanup functions
     */
    addEventListeners(listeners) {
      return listeners.map(({ target, type, listener, options }) => 
        this.addEventListener(target, type, listener, options)
      );
    }

    /**
     * Add event listener that fires only once
     * @param {EventTarget} target - Event target
     * @param {string} type - Event type
     * @param {Function} listener - Event listener
     * @param {Object} options - Event options
     * @returns {Promise} - Promise that resolves when event fires
     */
    once(target, type, listener, options = {}) {
      return new Promise((resolve, reject) => {
        const cleanup = this.addEventListener(target, type, (event) => {
          try {
            const result = listener(event);
            cleanup();
            resolve(result);
          } catch (error) {
            cleanup();
            reject(error);
          }
        }, { ...options, once: true });
      });
    }

    /**
     * Add throttled event listener
     * @param {EventTarget} target - Event target
     * @param {string} type - Event type
     * @param {Function} listener - Event listener
     * @param {number} delay - Throttle delay in ms
     * @param {Object} options - Event options
     * @returns {Function} - Cleanup function
     */
    addThrottledListener(target, type, listener, delay = 100, options = {}) {
      const throttledListener = window.commonUtils.throttle(listener, delay);
      return this.addEventListener(target, type, throttledListener, options);
    }

    /**
     * Add debounced event listener
     * @param {EventTarget} target - Event target
     * @param {string} type - Event type
     * @param {Function} listener - Event listener
     * @param {number} delay - Debounce delay in ms
     * @param {Object} options - Event options
     * @returns {Function} - Cleanup function
     */
    addDebouncedListener(target, type, listener, delay = 100, options = {}) {
      const debouncedListener = window.commonUtils.debounce(listener, delay);
      return this.addEventListener(target, type, debouncedListener, options);
    }

    /**
     * Add conditional event listener that only fires when condition is met
     * @param {EventTarget} target - Event target
     * @param {string} type - Event type
     * @param {Function} listener - Event listener
     * @param {Function} condition - Condition function
     * @param {Object} options - Event options
     * @returns {Function} - Cleanup function
     */
    addConditionalListener(target, type, listener, condition, options = {}) {
      const conditionalListener = (event) => {
        if (condition(event)) {
          listener(event);
        }
      };
      return this.addEventListener(target, type, conditionalListener, options);
    }

    /**
     * Create event delegation handler
     * @param {EventTarget} target - Parent element
     * @param {string} type - Event type
     * @param {string} selector - Child selector
     * @param {Function} listener - Event listener
     * @param {Object} options - Event options
     * @returns {Function} - Cleanup function
     */
    delegate(target, type, selector, listener, options = {}) {
      const delegatedListener = (event) => {
        const matchedElement = event.target.closest(selector);
        if (matchedElement && target.contains(matchedElement)) {
          listener.call(matchedElement, event);
        }
      };
      return this.addEventListener(target, type, delegatedListener, options);
    }

    /**
     * Get statistics about managed listeners
     * @returns {Object} - Statistics object
     */
    getStats() {
      return {
        name: this.name,
        ...this.stats,
        activeListeners: this.listeners.size,
        isAborted: this.abortController.signal.aborted
      };
    }

    /**
     * Get detailed information about active listeners
     * @returns {Array} - Array of listener information
     */
    getActiveListeners() {
      return Array.from(this.listeners.values()).map(listener => ({
        target: listener.target.constructor.name,
        type: listener.type,
        timestamp: listener.timestamp,
        age: Date.now() - listener.timestamp
      }));
    }

    /**
     * Abort all managed event listeners
     */
    abort() {
      this.abortController.abort();
      this.stats.aborted = this.listeners.size;
      this.listeners.clear();
      logger.debug(`${this.name}: Aborted ${this.stats.aborted} event listeners`);
    }

    /**
     * Clean up all resources
     */
    destroy() {
      this.abort();
    }
  }

  /**
   * Global Event Manager Registry
   */
  class EventManagerRegistry {
    constructor() {
      this.managers = new Map();
    }

    /**
     * Create or get event manager
     * @param {string} name - Manager name
     * @returns {EventManager} - Event manager instance
     */
    getManager(name) {
      if (!this.managers.has(name)) {
        this.managers.set(name, new EventManager(name));
      }
      return this.managers.get(name);
    }

    /**
     * Destroy event manager
     * @param {string} name - Manager name
     */
    destroyManager(name) {
      const manager = this.managers.get(name);
      if (manager) {
        manager.destroy();
        this.managers.delete(name);
      }
    }

    /**
     * Get statistics for all managers
     * @returns {Object} - Statistics object
     */
    getStats() {
      const stats = {};
      for (const [name, manager] of this.managers.entries()) {
        stats[name] = manager.getStats();
      }
      return stats;
    }

    /**
     * Destroy all managers
     */
    destroyAll() {
      for (const [name, manager] of this.managers.entries()) {
        manager.destroy();
      }
      this.managers.clear();
    }
  }

  // Create global registry
  const registry = new EventManagerRegistry();

  // Cleanup on page unload
  window.addEventListener('beforeunload', () => {
    registry.destroyAll();
  });

  // Export to global scope
  window.EventManager = EventManager;
  window.eventManagerRegistry = registry;

  // Convenience functions
  window.createEventManager = (name) => new EventManager(name);
  window.getEventManager = (name) => registry.getManager(name);

})();
